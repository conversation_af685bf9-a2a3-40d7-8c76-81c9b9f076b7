' ===============================================================================
' GST TAX INVOICE SYSTEM - COMPREHENSIVE VBA SOLUTION
' ===============================================================================
'
' QUICK START GUIDE:
' 1. Copy and paste this entire code into a VBA module in Excel
' 2. Run the macro "StartGSTSystem" (or press Alt+F8 and select it)
' 3. The system will automatically create all required worksheets and setup
' 4. Your GST Tax Invoice system is ready to use!
'
' CLEAN MACRO LIST - VISIBLE FUNCTIONS (Alt+F8):
' 🚀 SETUP FUNCTIONS:
' - QuickSetup()                    : Ultra-simple setup (recommended first)
' - StartGSTSystem()                : Complete system with all features
' - StartGSTSystemMinimal()         : Basic setup for debugging
' - ShowAvailableFunctions()        : Help and function descriptions
'
' 🔘 BUTTON FUNCTIONS (for daily operations):
' - AddCustomerToWarehouseButton()  : Add customer from invoice to warehouse
' - AddNewItemRowButton()           : Add new item row to invoice table
' - NewInvoiceButton()              : Generate fresh invoice with next sequential number
' - SaveInvoiceButton()             : Save invoice record to Master sheet
' - PrintAsPDFButton()              : Export invoice as PDF to designated folder
' - PrintButton()                   : Save as PDF and send to printer
'
' 👥 DATA MANAGEMENT: (All customer data is managed through SaveInvoiceButton)
'
' 💡 20+ internal helper functions are HIDDEN for a professional, clean interface!
'
' SYSTEM FEATURES:
' - Auto-invoice numbering with year-based reset
' - Professional GST-compliant invoice format
' - Customer and HSN code management
' - Automatic tax calculations
' - Enhanced dropdown functionality (dropdown + manual entry)
' - Multi-item support with dynamic rows (minimum 4 default rows)
' - Enhanced state code dropdowns with "State Name - Code" format
' - Customer dropdowns in both receiver and consignee sections
' - Print and PDF export capabilities
' - Simplified dropdown-only approach for data entry
'
' SUPPORTING WORKSHEETS CREATED:
' - GST_Tax_Invoice_for_interstate : Main invoice sheet
' - Master                        : Invoice counter and settings
' - warehouse                     : Customer and HSN data storage
'
' ===============================================================================

' ████████████████████████████████████████████████████████████████████████████████
' 🚀 MAIN SETUP FUNCTIONS - USER INTERFACE
' ████████████████████████████████████████████████████████████████████████████████
' These are the PRIMARY functions users should run. All other functions are helpers.

Sub StartGSTSystem()
    ' Simple entry point for users - sets up everything automatically
    Call InitializeGSTSystem
End Sub

Sub QuickSetup()
    ' Ultra-simple setup function that should work without any prompts
    On Error GoTo ErrorHandler

    Application.ScreenUpdating = False
    Application.DisplayAlerts = False

    ' Delete any existing sheets first to start fresh
    Dim ws As Worksheet
    On Error Resume Next
    Set ws = ThisWorkbook.Sheets("GST_Tax_Invoice_for_interstate")
    If Not ws Is Nothing Then ws.Delete
    Set ws = ThisWorkbook.Sheets("Master")
    If Not ws Is Nothing Then ws.Delete
    Set ws = ThisWorkbook.Sheets("warehouse")
    If Not ws Is Nothing Then ws.Delete
    On Error GoTo ErrorHandler

    ' Create sheets in order
    Call CreateMasterSheet
    Call CreateWarehouseSheet
    Call CreateInvoiceSheet

    Application.ScreenUpdating = True
    Application.DisplayAlerts = True

    MsgBox "Quick setup complete! Three worksheets created:" & vbCrLf & _
           "1. GST_Tax_Invoice_for_interstate" & vbCrLf & _
           "2. Master" & vbCrLf & _
           "3. warehouse" & vbCrLf & vbCrLf & _
           "System is ready for use!", vbInformation, "Setup Complete"
    Exit Sub

ErrorHandler:
    Application.ScreenUpdating = True
    Application.DisplayAlerts = True
    MsgBox "Quick setup error: " & Err.Description, vbCritical, "Setup Error"
End Sub

Sub StartGSTSystemMinimal()
    ' Minimal initialization without data validation setup (for debugging)
    On Error GoTo ErrorHandler

    Application.ScreenUpdating = False
    Application.DisplayAlerts = False

    ' Step 1: Create all supporting worksheets first
    Call CreateMasterSheet
    Call CreateWarehouseSheet

    ' Step 2: Create the main invoice sheet
    Call CreateInvoiceSheet

    Application.ScreenUpdating = True
    Application.DisplayAlerts = True

    MsgBox "GST Tax Invoice System initialized successfully (minimal version)!" & vbCrLf & _
           "All supporting worksheets created." & vbCrLf & _
           "Data validation setup skipped for debugging.", vbInformation, "System Ready"

    Exit Sub

ErrorHandler:
    Application.ScreenUpdating = True
    Application.DisplayAlerts = True
    MsgBox "Error initializing GST system: " & Err.Description, vbCritical, "Initialization Error"
End Sub

Sub ShowAvailableFunctions()
    ' Display all available functions for the user
    Dim functionList As String
    functionList = "GST SYSTEM - COMPLETE FUNCTIONALITY:" & vbCrLf & vbCrLf

    functionList = functionList & "✨ CLEAN MACRO LIST (Alt+F8) - Only 17 Functions:" & vbCrLf & vbCrLf

    functionList = functionList & "🚀 SETUP FUNCTIONS:" & vbCrLf
    functionList = functionList & "• QuickSetup - Ultra-simple setup (recommended first)" & vbCrLf
    functionList = functionList & "• StartGSTSystem - Complete system with all features" & vbCrLf
    functionList = functionList & "• StartGSTSystemMinimal - Basic setup for debugging" & vbCrLf
    functionList = functionList & "• ShowAvailableFunctions - Show this help list" & vbCrLf & vbCrLf

    functionList = functionList & "🔘 BUTTON FUNCTIONS (Daily Operations):" & vbCrLf
    functionList = functionList & "• AddCustomerToWarehouseButton - Add customer to warehouse" & vbCrLf
    functionList = functionList & "• AddNewItemRowButton - Add new item row to invoice" & vbCrLf
    functionList = functionList & "• NewInvoiceButton - Generate fresh invoice with next number" & vbCrLf
    functionList = functionList & "• SaveInvoiceButton - Save invoice to Master sheet" & vbCrLf
    functionList = functionList & "• PrintAsPDFButton - Export as PDF to folder" & vbCrLf
    functionList = functionList & "• PrintButton - Save PDF + send to printer" & vbCrLf & vbCrLf

    functionList = functionList & "👥 DATA MANAGEMENT & UTILITIES:" & vbCrLf
    functionList = functionList & "• VerifyValidationSettings - Check manual editing capability" & vbCrLf
    functionList = functionList & "• All customer data managed through SaveInvoiceButton" & vbCrLf
    functionList = functionList & "• Enhanced dropdown functionality (dropdown + manual entry)" & vbCrLf
    functionList = functionList & "• State code dropdowns show simple numeric codes (37, 29, etc.)" & vbCrLf
    functionList = functionList & "• Customer dropdowns in both receiver and consignee sections" & vbCrLf & vbCrLf

    functionList = functionList & "🔒 PROFESSIONAL INTERFACE:" & vbCrLf
    functionList = functionList & "20+ internal helper functions are PRIVATE and hidden" & vbCrLf
    functionList = functionList & "for a clean, professional macro list!" & vbCrLf & vbCrLf

    functionList = functionList & "💡 TIP: Start with QuickSetup, then use button functions!"

    MsgBox functionList, vbInformation, "GST System - Complete & Clean"
End Sub

' ████████████████████████████████████████████████████████████████████████████████
' 🔧 INTERNAL SYSTEM FUNCTIONS - HIDDEN FROM MACRO LIST
' ████████████████████████████████████████████████████████████████████████████████
' These are PRIVATE helper functions that power the system. Users don't need to see these.

Private Sub InitializeGSTSystem()
    ' Master initialization function - creates all required worksheets and sets up the system
    On Error GoTo ErrorHandler

    Application.ScreenUpdating = False
    Application.DisplayAlerts = False

    Dim statusMsg As String
    statusMsg = "Initializing GST System..." & vbCrLf

    ' Step 1: Create all supporting worksheets first
    statusMsg = statusMsg & "Creating Master sheet..."
    Call CreateMasterSheet
    statusMsg = statusMsg & " ✓" & vbCrLf

    statusMsg = statusMsg & "Creating warehouse sheet..."
    Call CreateWarehouseSheet
    statusMsg = statusMsg & " ✓" & vbCrLf

    ' Step 2: Create the main invoice sheet
    statusMsg = statusMsg & "Creating invoice sheet..."
    Call CreateInvoiceSheet
    statusMsg = statusMsg & " ✓" & vbCrLf

    ' Step 3: Set up all data validation and dropdowns
    statusMsg = statusMsg & "Setting up data validation..."
    Dim invoiceWs As Worksheet
    Set invoiceWs = ThisWorkbook.Sheets("GST_Tax_Invoice_for_interstate")
    Call SetupDataValidation(invoiceWs)
    statusMsg = statusMsg & " ✓" & vbCrLf

    statusMsg = statusMsg & "Setting up customer dropdown..."
    Call SetupCustomerDropdown(invoiceWs)
    statusMsg = statusMsg & " ✓" & vbCrLf

    statusMsg = statusMsg & "Setting up HSN dropdown..."
    Call SetupHSNDropdown(invoiceWs)
    statusMsg = statusMsg & " ✓" & vbCrLf

    statusMsg = statusMsg & "Setting up tax calculations..."
    Call SetupTaxCalculationFormulas(invoiceWs)
    statusMsg = statusMsg & " ✓" & vbCrLf

    Application.ScreenUpdating = True
    Application.DisplayAlerts = True

    MsgBox "GST Tax Invoice System initialized successfully!" & vbCrLf & _
           "All supporting worksheets created and configured." & vbCrLf & _
           "You can now use the invoice system.", vbInformation, "System Ready"

    Exit Sub

ErrorHandler:
    Application.ScreenUpdating = True
    Application.DisplayAlerts = True
    MsgBox "Error initializing GST system at: " & statusMsg & vbCrLf & _
           "Error: " & Err.Description & vbCrLf & _
           "Line: " & Erl, vbCritical, "Initialization Error"
End Sub

' ===== DEBUGGING AND TROUBLESHOOTING =====

Private Sub DebugInitialization()
    ' Step-by-step debugging of the initialization process
    On Error GoTo ErrorHandler

    Application.ScreenUpdating = False
    Application.DisplayAlerts = False

    Dim debugMsg As String
    debugMsg = "Debug Initialization Process:" & vbCrLf & vbCrLf

    ' Step 1: Test Master sheet creation
    debugMsg = debugMsg & "Step 1: Creating Master sheet... "
    Call CreateMasterSheet
    If WorksheetExists("Master") Then
        debugMsg = debugMsg & "✓ SUCCESS" & vbCrLf
    Else
        debugMsg = debugMsg & "✗ FAILED" & vbCrLf
    End If

    ' Step 2: Test warehouse sheet creation
    debugMsg = debugMsg & "Step 2: Creating warehouse sheet... "
    Call CreateWarehouseSheet
    If WorksheetExists("warehouse") Then
        debugMsg = debugMsg & "✓ SUCCESS" & vbCrLf
    Else
        debugMsg = debugMsg & "✗ FAILED" & vbCrLf
    End If

    ' Step 3: Test invoice sheet creation
    debugMsg = debugMsg & "Step 3: Creating invoice sheet... "
    Call CreateInvoiceSheet
    If WorksheetExists("GST_Tax_Invoice_for_interstate") Then
        debugMsg = debugMsg & "✓ SUCCESS" & vbCrLf
    Else
        debugMsg = debugMsg & "✗ FAILED" & vbCrLf
    End If

    Application.ScreenUpdating = True
    Application.DisplayAlerts = True

    MsgBox debugMsg, vbInformation, "Debug Results"
    Exit Sub

ErrorHandler:
    Application.ScreenUpdating = True
    Application.DisplayAlerts = True
    MsgBox "Debug error at: " & debugMsg & vbCrLf & "Error: " & Err.Description, vbCritical, "Debug Error"
End Sub

' ===== SYSTEM TESTING AND VERIFICATION =====

Private Sub TestGSTSystem()
    ' Comprehensive test function to verify the system works properly
    On Error GoTo ErrorHandler

    Application.ScreenUpdating = False
    Application.DisplayAlerts = False

    Dim testResults As String
    testResults = "GST System Test Results:" & vbCrLf & vbCrLf

    ' Test 1: Initialize the system
    testResults = testResults & "1. Initializing GST System... "
    Call InitializeGSTSystem
    testResults = testResults & "✓ PASSED" & vbCrLf

    ' Test 2: Verify all worksheets exist
    testResults = testResults & "2. Checking worksheet creation... "
    If WorksheetExists("GST_Tax_Invoice_for_interstate") And _
       WorksheetExists("Master") And _
       WorksheetExists("warehouse") Then
        testResults = testResults & "✓ PASSED" & vbCrLf
    Else
        testResults = testResults & "✗ FAILED" & vbCrLf
    End If

    ' Test 3: Test invoice numbering
    testResults = testResults & "3. Testing invoice numbering... "
    Dim testInvoiceNum As String
    testInvoiceNum = GetNextInvoiceNumber()
    If testInvoiceNum <> "" Then
        testResults = testResults & "✓ PASSED (" & testInvoiceNum & ")" & vbCrLf
    Else
        testResults = testResults & "✗ FAILED" & vbCrLf
    End If

    ' Test 4: Test data validation setup
    testResults = testResults & "4. Testing data validation... "
    Dim invoiceWs As Worksheet
    Set invoiceWs = GetOrCreateWorksheet("GST_Tax_Invoice_for_interstate")
    Call SetupDataValidation(invoiceWs)
    testResults = testResults & "✓ PASSED" & vbCrLf

    Application.ScreenUpdating = True
    Application.DisplayAlerts = True

    testResults = testResults & vbCrLf & "All tests completed successfully!" & vbCrLf & _
                  "The GST system is ready for use."

    MsgBox testResults, vbInformation, "System Test Complete"
    Exit Sub

ErrorHandler:
    Application.ScreenUpdating = True
    Application.DisplayAlerts = True
    MsgBox "Test failed with error: " & Err.Description, vbCritical, "Test Error"
End Sub

' ===== HELPER FUNCTIONS FOR SAFE WORKSHEET ACCESS =====

Private Function GetOrCreateWorksheet(sheetName As String) As Worksheet
    ' Safely get or create a worksheet
    Dim ws As Worksheet

    On Error Resume Next
    Set ws = ThisWorkbook.Sheets(sheetName)
    On Error GoTo 0

    If ws Is Nothing Then
        Set ws = ThisWorkbook.Sheets.Add(After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.Count))
        ws.Name = sheetName
    End If

    Set GetOrCreateWorksheet = ws
End Function

' ▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓
' 🔧 UTILITY FUNCTIONS
' ▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓

Private Function WorksheetExists(sheetName As String) As Boolean
    ' Check if a worksheet exists
    Dim ws As Worksheet
    On Error Resume Next
    Set ws = ThisWorkbook.Sheets(sheetName)
    On Error GoTo 0
    WorksheetExists = Not (ws Is Nothing)
End Function

Private Sub EnsureAllSupportingWorksheetsExist()
    ' Ensure all required supporting worksheets exist
    On Error Resume Next

    ' Create Master sheet if it doesn't exist
    If Not WorksheetExists("Master") Then
        Call CreateMasterSheet
    End If

    ' Create warehouse sheet if it doesn't exist
    If Not WorksheetExists("warehouse") Then
        Call CreateWarehouseSheet
    End If

    On Error GoTo 0
End Sub

' ▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓
' 📋 WORKSHEET CREATION FUNCTIONS
' ▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓

Private Sub CreateInvoiceSheet()
    Dim ws As Worksheet
    Dim i As Long
    Dim headers As Variant
    Dim itemData As Variant

    ' Suppress Excel alerts to prevent merge cells warning
    Application.DisplayAlerts = False
    Dim receiverData(0 To 4, 0 To 1) As Variant
    Dim consigneeData(0 To 4, 0 To 1) As Variant

    ' --- Setup with comprehensive error handling ---
    On Error GoTo ErrorHandler

    ' Try to get the sheet
    Set ws = Nothing
    On Error Resume Next
    Set ws = ThisWorkbook.Sheets("GST_Tax_Invoice_for_interstate")
    On Error GoTo 0

    ' If the sheet doesn't exist, create it. If it exists, clear it completely.
    If ws Is Nothing Then
        Set ws = ThisWorkbook.Sheets.Add(After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.Count))
        ws.Name = "GST_Tax_Invoice_for_interstate"
    Else
        ' Complete cleanup of existing sheet
        On Error Resume Next
        ws.Cells.UnMerge
        ws.Cells.Clear
        On Error GoTo 0
    End If

    ' Activate the sheet safely
    On Error Resume Next
    ws.Activate
    On Error GoTo 0

    ' --- Main Formatting Block ---
    With ws
        ' Set column widths safely
        On Error Resume Next
        .Columns(1).ColumnWidth = 5    ' Column A - Sr.No.
        .Columns(2).ColumnWidth = 12   ' Column B - Description of Goods/Services
        .Columns(3).ColumnWidth = 12   ' Column C - HSN/SAC Code
        .Columns(4).ColumnWidth = 9    ' Column D - Quantity
        .Columns(5).ColumnWidth = 7    ' Column E - UOM
        .Columns(6).ColumnWidth = 10   ' Column F - Rate
        .Columns(7).ColumnWidth = 8    ' Column G - Amount
        .Columns(8).ColumnWidth = 10   ' Column H - Taxable Value
        .Columns(9).ColumnWidth = 6    ' Column I - IGST Rate
        .Columns(10).ColumnWidth = 10  ' Column J - IGST Amount
        .Columns(11).ColumnWidth = 12  ' Column K - Total Amount
        On Error GoTo 0

        ' Set default font for all cells (before applying specific formatting)
        On Error Resume Next
        .Cells.Font.Name = "Segoe UI"
        .Cells.Font.Size = 10
        .Cells.Font.Color = RGB(26, 26, 26)
        On Error GoTo 0

        ' Create header sections with premium professional styling
        Call CreateHeaderRow(ws, 1, "A1:K1", "ORIGINAL FOR RECIPIENT", 11, True, RGB(47, 80, 97), RGB(255, 255, 255), 20)
        Call CreateHeaderRow(ws, 2, "A2:K2", "KAVERI TRADERS", 22, True, RGB(47, 80, 97), RGB(255, 255, 255), 32)
        Call CreateHeaderRow(ws, 3, "A3:K3", "191, Guduru, Pagadalapalli, Idulapalli, Tirupati, Andhra Pradesh - 524409", 10, True, RGB(245, 245, 245), RGB(26, 26, 26), 22)
        Call CreateHeaderRow(ws, 4, "A4:K4", "GSTIN: 37HERPB7733F1Z5", 12, True, RGB(245, 245, 245), RGB(26, 26, 26), 22)
        Call CreateHeaderRow(ws, 5, "A5:K5", "Email: <EMAIL>", 10, True, RGB(245, 245, 245), RGB(26, 26, 26), 20)

        ' Remove horizontal borders between header rows for print/PDF appearance
        On Error Resume Next
        ' Remove bottom border of row 2 (between row 2 and 3)
        .Range("A2:K2").Borders(xlEdgeBottom).LineStyle = xlNone
        ' Remove bottom border of row 3 (between row 3 and 4)
        .Range("A3:K3").Borders(xlEdgeBottom).LineStyle = xlNone
        ' Remove bottom border of row 4 (between row 4 and 5)
        .Range("A4:K4").Borders(xlEdgeBottom).LineStyle = xlNone
        On Error GoTo 0

        ' Row 6: TAX-INVOICE header
        Call CreateHeaderRow(ws, 6, "A6:G6", "TAX-INVOICE", 20, True, RGB(240, 240, 240), RGB(0, 0, 0), 28)
        Call CreateHeaderRow(ws, 6, "H6:K6", "Original for Recipient" & vbLf & "Duplicate for Supplier/Transporter" & vbLf & "Triplicate for Supplier", 8, True, RGB(250, 250, 250), RGB(0, 0, 0), 42)

        ' Enable text wrapping for the right section and ensure center alignment for TAX-INVOICE
        On Error Resume Next
        .Range("A6:G6").HorizontalAlignment = xlCenter
        .Range("A6:G6").VerticalAlignment = xlCenter
        .Range("H6:K6").WrapText = True
        On Error GoTo 0

        ' --- Invoice Details with Merged Cells ---
        On Error Resume Next

        ' Row 7: Invoice No., Transport Mode, Challan No.
        .Range("A7:B7").Merge
        .Range("A7").Value = "Invoice No."
        .Range("A7").Font.Bold = True
        .Range("A7").HorizontalAlignment = xlLeft
        .Range("A7").Interior.Color = RGB(245, 245, 245)
        .Range("A7").Font.Color = RGB(26, 26, 26)
        .Range("C7").Value = ""
        .Range("C7").Font.Bold = True
        .Range("C7").Font.Color = RGB(220, 20, 60)  ' Red color for user input
        .Range("C7").HorizontalAlignment = xlCenter
        .Range("C7").VerticalAlignment = xlCenter

        .Range("D7:E7").Merge
        .Range("D7").Value = "Transport Mode"
        .Range("D7").Font.Bold = True
        .Range("D7").HorizontalAlignment = xlLeft
        .Range("D7").Interior.Color = RGB(245, 245, 245)
        .Range("D7").Font.Color = RGB(26, 26, 26)
        .Range("F7:G7").Merge
        .Range("F7").Value = "By Lorry"
        .Range("F7").HorizontalAlignment = xlLeft

        .Range("H7:I7").Merge
        .Range("H7").Value = "Challan No."
        .Range("H7").Font.Bold = True
        .Range("H7").HorizontalAlignment = xlLeft
        .Range("H7").Interior.Color = RGB(245, 245, 245)
        .Range("H7").Font.Color = RGB(26, 26, 26)
        .Range("J7:K7").Merge
        .Range("J7").Value = ""
        .Range("J7").HorizontalAlignment = xlLeft

        ' Row 8: Invoice Date, Vehicle Number, Transporter Name
        .Range("A8:B8").Merge
        .Range("A8").Value = "Invoice Date"
        .Range("A8").Font.Bold = True
        .Range("A8").HorizontalAlignment = xlLeft
        .Range("A8").Interior.Color = RGB(245, 245, 245)
        .Range("A8").Font.Color = RGB(26, 26, 26)
        .Range("C8").Value = ""
        .Range("C8").Font.Bold = True
        .Range("C8").HorizontalAlignment = xlLeft

        .Range("D8:E8").Merge
        .Range("D8").Value = "Vehicle Number"
        .Range("D8").Font.Bold = True
        .Range("D8").HorizontalAlignment = xlLeft
        .Range("D8").Interior.Color = RGB(245, 245, 245)
        .Range("D8").Font.Color = RGB(26, 26, 26)
        .Range("F8:G8").Merge
        .Range("F8").Value = ""
        .Range("F8").HorizontalAlignment = xlLeft

        .Range("H8:I8").Merge
        .Range("H8").Value = "Transporter Name"
        .Range("H8").Font.Bold = True
        .Range("H8").HorizontalAlignment = xlLeft
        .Range("H8").Interior.Color = RGB(245, 245, 245)
        .Range("H8").Font.Color = RGB(26, 26, 26)
        .Range("J8:K8").Merge
        .Range("J8").Value = "NARENDRA"
        .Range("J8").HorizontalAlignment = xlLeft

        ' Row 9: State, Date of Supply, L.R Number
        .Range("A9:B9").Merge
        .Range("A9").Value = "State"
        .Range("A9").Font.Bold = True
        .Range("A9").HorizontalAlignment = xlLeft
        .Range("A9").Interior.Color = RGB(245, 245, 245)
        .Range("A9").Font.Color = RGB(26, 26, 26)
        .Range("C9").Value = "Andhra Pradesh"
        .Range("C9").HorizontalAlignment = xlLeft

        .Range("D9:E9").Merge
        .Range("D9").Value = "Date of Supply"
        .Range("D9").Font.Bold = True
        .Range("D9").HorizontalAlignment = xlLeft
        .Range("D9").Interior.Color = RGB(245, 245, 245)
        .Range("D9").Font.Color = RGB(26, 26, 26)
        .Range("F9:G9").Merge
        .Range("F9").Value = ""
        .Range("F9").HorizontalAlignment = xlLeft

        .Range("H9:I9").Merge
        .Range("H9").Value = "L.R Number"
        .Range("H9").Font.Bold = True
        .Range("H9").HorizontalAlignment = xlLeft
        .Range("H9").Interior.Color = RGB(245, 245, 245)
        .Range("H9").Font.Color = RGB(26, 26, 26)
        .Range("J9:K9").Merge
        .Range("J9").Value = ""
        .Range("J9").HorizontalAlignment = xlLeft

        ' Row 10: State Code, Place of Supply, P.O Number
        .Range("A10:B10").Merge
        .Range("A10").Value = "State Code"
        .Range("A10").Font.Bold = True
        .Range("A10").HorizontalAlignment = xlLeft
        .Range("A10").Interior.Color = RGB(245, 245, 245)
        .Range("A10").Font.Color = RGB(26, 26, 26)
        .Range("C10").Value = "37"
        .Range("C10").HorizontalAlignment = xlLeft

        .Range("D10:E10").Merge
        .Range("D10").Value = "Place of Supply"
        .Range("D10").Font.Bold = True
        .Range("D10").HorizontalAlignment = xlLeft
        .Range("D10").Interior.Color = RGB(245, 245, 245)
        .Range("D10").Font.Color = RGB(26, 26, 26)
        .Range("F10:G10").Merge
        .Range("F10").Value = ""
        .Range("F10").HorizontalAlignment = xlLeft

        .Range("H10:I10").Merge
        .Range("H10").Value = "P.O Number"
        .Range("H10").Font.Bold = True
        .Range("H10").HorizontalAlignment = xlLeft
        .Range("H10").Interior.Color = RGB(245, 245, 245)
        .Range("H10").Font.Color = RGB(26, 26, 26)
        .Range("J10:K10").Merge
        .Range("J10").Value = ""
        .Range("J10").HorizontalAlignment = xlLeft

        ' Apply borders and formatting with professional color
        .Range("A7:K10").Borders.LineStyle = xlContinuous
        .Range("A7:K10").Borders.Color = RGB(204, 204, 204)
        For i = 7 To 10
            .Rows(i).RowHeight = 22
        Next i
        On Error GoTo 0

        ' --- Party Details (Professional Styling) ---
        Call CreateHeaderRow(ws, 11, "A11:F11", "Details of Receiver (Billed to)", 10, True, RGB(245, 245, 245), RGB(26, 26, 26), 26)
        Call CreateHeaderRow(ws, 11, "G11:K11", "Details of Consignee (Shipped to)", 10, True, RGB(245, 245, 245), RGB(26, 26, 26), 26)

        ' Set center alignment for row 11 content (both horizontal and vertical)
        On Error Resume Next
        .Range("A11:F11").HorizontalAlignment = xlCenter
        .Range("A11:F11").VerticalAlignment = xlCenter
        .Range("G11:K11").HorizontalAlignment = xlCenter
        .Range("G11:K11").VerticalAlignment = xlCenter
        On Error GoTo 0

        ' --- Party Details with Merged Cells ---
        On Error Resume Next

        ' Row 12: Name fields
        .Range("A12:B12").Merge
        .Range("A12").Value = "Name:"
        .Range("A12").Font.Bold = True
        .Range("A12").HorizontalAlignment = xlLeft
        .Range("A12").Interior.Color = RGB(245, 245, 245)
        .Range("A12").Font.Color = RGB(26, 26, 26)
        .Range("C12:F12").Merge
        .Range("C12").Value = ""
        .Range("C12").HorizontalAlignment = xlLeft

        .Range("G12:H12").Merge
        .Range("G12").Value = "Name:"
        .Range("G12").Font.Bold = True
        .Range("G12").HorizontalAlignment = xlLeft
        .Range("G12").Interior.Color = RGB(245, 245, 245)
        .Range("G12").Font.Color = RGB(26, 26, 26)
        .Range("I12:K12").Merge
        .Range("I12").Value = ""
        .Range("I12").HorizontalAlignment = xlLeft

        ' Row 13: Address fields
        .Range("A13:B13").Merge
        .Range("A13").Value = "Address:"
        .Range("A13").Font.Bold = True
        .Range("A13").HorizontalAlignment = xlLeft
        .Range("A13").Interior.Color = RGB(245, 245, 245)
        .Range("A13").Font.Color = RGB(26, 26, 26)
        .Range("C13:F13").Merge
        .Range("C13").Value = ""
        .Range("C13").HorizontalAlignment = xlLeft

        .Range("G13:H13").Merge
        .Range("G13").Value = "Address:"
        .Range("G13").Font.Bold = True
        .Range("G13").HorizontalAlignment = xlLeft
        .Range("G13").Interior.Color = RGB(245, 245, 245)
        .Range("G13").Font.Color = RGB(26, 26, 26)
        .Range("I13:K13").Merge
        .Range("I13").Value = ""
        .Range("I13").HorizontalAlignment = xlLeft

        ' Row 14: GSTIN fields
        .Range("A14:B14").Merge
        .Range("A14").Value = "GSTIN:"
        .Range("A14").Font.Bold = True
        .Range("A14").HorizontalAlignment = xlLeft
        .Range("A14").Interior.Color = RGB(245, 245, 245)
        .Range("A14").Font.Color = RGB(26, 26, 26)
        .Range("C14:F14").Merge
        .Range("C14").Value = ""
        .Range("C14").HorizontalAlignment = xlLeft

        .Range("G14:H14").Merge
        .Range("G14").Value = "GSTIN:"
        .Range("G14").Font.Bold = True
        .Range("G14").HorizontalAlignment = xlLeft
        .Range("G14").Interior.Color = RGB(245, 245, 245)
        .Range("G14").Font.Color = RGB(26, 26, 26)
        .Range("I14:K14").Merge
        .Range("I14").Value = ""
        .Range("I14").HorizontalAlignment = xlLeft

        ' Row 15: State fields
        .Range("A15:B15").Merge
        .Range("A15").Value = "State:"
        .Range("A15").Font.Bold = True
        .Range("A15").HorizontalAlignment = xlLeft
        .Range("A15").Interior.Color = RGB(245, 245, 245)
        .Range("A15").Font.Color = RGB(26, 26, 26)
        .Range("C15:F15").Merge
        .Range("C15").Value = ""
        .Range("C15").HorizontalAlignment = xlLeft

        .Range("G15:H15").Merge
        .Range("G15").Value = "State:"
        .Range("G15").Font.Bold = True
        .Range("G15").HorizontalAlignment = xlLeft
        .Range("G15").Interior.Color = RGB(245, 245, 245)
        .Range("G15").Font.Color = RGB(26, 26, 26)
        .Range("I15:K15").Merge
        .Range("I15").Value = ""
        .Range("I15").HorizontalAlignment = xlLeft

        ' Row 16: State Code fields
        .Range("A16:B16").Merge
        .Range("A16").Value = "State Code:"
        .Range("A16").Font.Bold = True
        .Range("A16").HorizontalAlignment = xlLeft
        .Range("A16").Interior.Color = RGB(245, 245, 245)
        .Range("A16").Font.Color = RGB(26, 26, 26)
        .Range("C16:F16").Merge
        .Range("C16").Value = ""
        .Range("C16").HorizontalAlignment = xlLeft

        .Range("G16:H16").Merge
        .Range("G16").Value = "State Code:"
        .Range("G16").Font.Bold = True
        .Range("G16").HorizontalAlignment = xlLeft
        .Range("G16").Interior.Color = RGB(245, 245, 245)
        .Range("G16").Font.Color = RGB(26, 26, 26)
        .Range("I16:K16").Merge
        .Range("I16").Value = ""
        .Range("I16").HorizontalAlignment = xlLeft

        ' Apply borders and formatting for rows 12-16 with professional color
        .Range("A12:K16").Borders.LineStyle = xlContinuous
        .Range("A12:K16").Borders.Color = RGB(204, 204, 204)
        For i = 12 To 16
            .Rows(i).RowHeight = 22
        Next i
        On Error GoTo 0

        ' --- Item Table (Simplified) ---
        On Error Resume Next

        ' Headers
        headers = Array("Sr.No.", "Description of Goods/Services", "HSN/SAC Code", "Quantity", "UOM", "Rate (Rs.)", "Amount (Rs.)", "Taxable Value (Rs.)", "IGST Rate (%)", "IGST Amount (Rs.)", "Total Amount (Rs.)")
        For i = 0 To UBound(headers)
            With .Cells(17, i + 1)
                .Value = headers(i)
                .Font.Bold = True
                .Font.Size = 10
                .Interior.Color = RGB(245, 245, 245)
                .Font.Color = RGB(26, 26, 26)
                .WrapText = True
                .HorizontalAlignment = xlCenter
                .Borders.LineStyle = xlContinuous
                .Borders.Color = RGB(204, 204, 204)
            End With
        Next i
        .Rows(17).RowHeight = 45

        ' Item data
        itemData = Array("1", "Casuarina Wood", "", "", "", "", "", "", "", "", "")
        For i = 0 To UBound(itemData)
            With .Cells(18, i + 1)
                .Value = itemData(i)
                .Borders.LineStyle = xlContinuous
                .Borders.Color = RGB(204, 204, 204)
                .Font.Size = 10
                .Interior.Color = RGB(250, 250, 250)
                If i = 0 Or i = 2 Or i = 3 Or i = 4 Then
                    .HorizontalAlignment = xlCenter
                ElseIf i = 1 Then
                    .HorizontalAlignment = xlLeft
                ElseIf i >= 5 Then
                    .HorizontalAlignment = xlRight
                    .Font.Bold = True
                End If
            End With
        Next i
        .Rows(18).RowHeight = 32

        ' Setup automatic tax calculation formulas
        Call SetupTaxCalculationFormulas(ws)

        ' Empty rows with alternating colors
        For i = 19 To 21
            .Range("A" & i & ":K" & i).Borders.LineStyle = xlContinuous
            .Range("A" & i & ":K" & i).Borders.Color = RGB(204, 204, 204)
            If i Mod 2 = 0 Then
                .Range("A" & i & ":K" & i).Interior.Color = RGB(250, 250, 250)
            Else
                .Range("A" & i & ":K" & i).Interior.Color = RGB(255, 255, 255)
            End If
            .Rows(i).RowHeight = 24
        Next i
        On Error GoTo 0

        ' --- Row 22 Total Quantity Section (Restored) ---
        On Error Resume Next

        ' Merge A22:C22 for "Total Quantity" label
        .Range("A22:C22").Merge
        .Range("A22").Value = "Total Quantity"
        .Range("A22").Font.Bold = True
        .Range("A22").HorizontalAlignment = xlCenter
        .Range("A22").VerticalAlignment = xlBottom
        .Range("A22").Interior.Color = RGB(234, 234, 234)
        .Range("A22").Font.Color = RGB(26, 26, 26)

        ' Cell D22 for quantity value
        .Range("D22").Value = ""
        .Range("D22").Font.Bold = True
        .Range("D22").HorizontalAlignment = xlCenter
        .Range("D22").Interior.Color = RGB(234, 234, 234)

        ' Merge E22:F22 for "Sub Total" label
        .Range("E22:F22").Merge
        .Range("E22").Value = "Sub Total:"
        .Range("E22").Font.Bold = True
        .Range("E22").HorizontalAlignment = xlRight
        .Range("E22").Interior.Color = RGB(234, 234, 234)
        .Range("E22").Font.Color = RGB(26, 26, 26)

        ' Individual cells for amounts
        .Range("G22").Value = ""
        .Range("G22").Font.Bold = True
        .Range("G22").HorizontalAlignment = xlRight
        .Range("G22").Interior.Color = RGB(234, 234, 234)

        .Range("H22").Value = ""
        .Range("H22").Font.Bold = True
        .Range("H22").HorizontalAlignment = xlRight
        .Range("H22").Interior.Color = RGB(234, 234, 234)

        ' Merge I22:J22 for IGST amount
        .Range("I22:J22").Merge
        .Range("I22").Value = ""
        .Range("I22").Font.Bold = True
        .Range("I22").HorizontalAlignment = xlRight

        ' Cell K22 for total amount
        .Range("K22").Value = ""
        .Range("K22").Font.Bold = True
        .Range("K22").HorizontalAlignment = xlRight

        ' Apply formatting to the entire row with professional styling
        .Range("A22:K22").Interior.Color = RGB(234, 234, 234)
        .Range("A22:K22").Borders.LineStyle = xlContinuous
        .Range("A22:K22").Borders.Color = RGB(204, 204, 204)
        .Rows(22).RowHeight = 26
        On Error GoTo 0

        ' --- Row 23-25 Total Invoice Amount in Words Section (Restored) ---
        On Error Resume Next

        ' Row 23: Header for "Total Invoice Amount in Words"
        .Range("A23:G23").Merge
        .Range("A23").Value = "Total Invoice Amount in Words"
        .Range("A23").Font.Bold = True
        .Range("A23").Font.Size = 12
        .Range("A23").HorizontalAlignment = xlCenter
        .Range("A23").Interior.Color = RGB(255, 255, 0)
        .Range("A23:G23").Borders.LineStyle = xlContinuous
        .Rows(23).RowHeight = 25

        ' Rows 24-25: Amount in words content (merged across 2 rows)
        .Range("A24:G25").Merge
        .Range("A24").Value = ""
        .Range("A24").Font.Bold = True
        .Range("A24").Font.Size = 14
        .Range("A24").HorizontalAlignment = xlCenter
        .Range("A24").VerticalAlignment = xlCenter
        .Range("A24").Interior.Color = RGB(255, 255, 230)
        .Range("A24").Borders.LineStyle = xlContinuous
        .Range("A24").WrapText = True
        .Rows(24).RowHeight = 22
        .Rows(25).RowHeight = 22

        ' Tax summary on the right (columns H-K, rows 23-25) with merged cells

        ' Row 23: Total Before Tax
        .Range("H23:J23").Merge
        .Range("H23").Value = "Total Amount Before Tax:"
        .Range("H23").Font.Bold = True
        .Range("H23").Font.Size = 10
        .Range("H23").HorizontalAlignment = xlLeft
        .Range("H23").Interior.Color = RGB(245, 245, 245)
        .Range("H23").Font.Color = RGB(26, 26, 26)

        .Range("K23").Value = ""
        .Range("K23").Font.Bold = True
        .Range("K23").HorizontalAlignment = xlRight
        .Range("K23").Interior.Color = RGB(216, 222, 233)

        ' Row 24: CGST @ 0%
        .Range("H24:J24").Merge
        .Range("H24").Value = "CGST :"
        .Range("H24").Font.Bold = True
        .Range("H24").Font.Size = 10
        .Range("H24").HorizontalAlignment = xlLeft
        .Range("H24").Interior.Color = RGB(245, 245, 245)
        .Range("H24").Font.Color = RGB(26, 26, 26)

        .Range("K24").Value = ""
        .Range("K24").Font.Bold = True
        .Range("K24").HorizontalAlignment = xlRight
        .Range("K24").Interior.Color = RGB(216, 222, 233)

        ' Row 25: SGST @ 0%
        .Range("H25:J25").Merge
        .Range("H25").Value = "SGST :"
        .Range("H25").Font.Bold = True
        .Range("H25").Font.Size = 10
        .Range("H25").HorizontalAlignment = xlLeft
        .Range("H25").Interior.Color = RGB(245, 245, 245)
        .Range("H25").Font.Color = RGB(26, 26, 26)

        .Range("K25").Value = ""
        .Range("K25").Font.Bold = True
        .Range("K25").HorizontalAlignment = xlRight
        .Range("K25").Interior.Color = RGB(216, 222, 233)

        ' Apply borders to amount in words and tax summary sections
        .Range("H23:K25").Borders.LineStyle = xlContinuous
        .Range("H23:K25").Borders.Color = RGB(204, 204, 204)
        On Error GoTo 0

        ' Row 26: Header for "Terms and Conditions"
        .Range("A26:G26").Merge
        .Range("A26").Value = "Terms and Conditions"
        .Range("A26").Font.Bold = True
        .Range("A26").Font.Size = 12
        .Range("A26").HorizontalAlignment = xlCenter
        .Range("A26").Interior.Color = RGB(255, 255, 0)
        .Range("A26:G26").Borders.LineStyle = xlContinuous
        .Rows(26).RowHeight = 25

        ' Rows 27-30: Terms and conditions content (merged across 4 rows)
        .Range("A27:G30").Merge
        .Range("A27").Value = "1. This is an electronically generated invoice." & vbLf & _
                             "2. All disputes are subject to GUDUR jurisdiction only." & vbLf & _
                             "3. If the Consignee makes any Inter State Sales, he has to pay GST himself." & vbLf & _
                             "4. Goods once sold cannot be taken back or exchanged." & vbLf & _
                             "5. Payment terms: As per agreement between buyer and seller."
        .Range("A27").Font.Size = 10
        .Range("A27").HorizontalAlignment = xlLeft
        .Range("A27").VerticalAlignment = xlTop
        .Range("A27").Interior.Color = RGB(255, 255, 245)
        .Range("A27").Borders.LineStyle = xlContinuous
        .Range("A27").WrapText = True
        For i = 27 To 30
            .Rows(i).RowHeight = 20
        Next i

        ' Tax summary on the right (columns H-K, rows 26-30) with merged cells

        ' Row 26: IGST @ 12% (highlighted)
        .Range("H26:J26").Merge
        .Range("H26").Value = "IGST :"
        .Range("H26").Font.Bold = True
        .Range("H26").Font.Size = 10
        .Range("H26").HorizontalAlignment = xlLeft
        .Range("H26").Interior.Color = RGB(255, 255, 200)
        .Range("H26").Font.Color = RGB(26, 26, 26)

        .Range("K26").Value = ""
        .Range("K26").Font.Bold = True
        .Range("K26").HorizontalAlignment = xlRight
        .Range("K26").Interior.Color = RGB(255, 255, 200)

        ' Row 27: CESS @ 0%
        .Range("H27:J27").Merge
        .Range("H27").Value = "CESS :"
        .Range("H27").Font.Bold = True
        .Range("H27").Font.Size = 10
        .Range("H27").HorizontalAlignment = xlLeft
        .Range("H27").Interior.Color = RGB(245, 245, 245)
        .Range("H27").Font.Color = RGB(26, 26, 26)

        .Range("K27").Value = ""
        .Range("K27").Font.Bold = True
        .Range("K27").HorizontalAlignment = xlRight
        .Range("K27").Interior.Color = RGB(216, 222, 233)

        ' Row 28: Total Tax (highlighted)
        .Range("H28:J28").Merge
        With .Range("H28")
            .Value = "Total Tax:"
            .Font.Bold = True
            .Font.Size = 10
            .Interior.Color = RGB(240, 240, 240)
            .Font.Color = RGB(26, 26, 26)
            .HorizontalAlignment = xlLeft
            .VerticalAlignment = xlCenter
        End With

        .Range("K28").Value = ""
        .Range("K28").Font.Bold = True
        .Range("K28").HorizontalAlignment = xlRight
        .Range("K28").Interior.Color = RGB(240, 240, 240)

        ' Rows 29-30: Total Amount After Tax (merged across 2 rows for enhanced prominence)
        .Range("H29:J30").Merge
        .Range("H29").Value = "Total Amount After Tax:"
        .Range("H29").Font.Bold = True
        .Range("H29").Font.Size = 10
        .Range("H29").HorizontalAlignment = xlLeft
        .Range("H29").VerticalAlignment = xlCenter
        .Range("H29").Interior.Color = RGB(255, 255, 180)
        .Range("H29").Font.Color = RGB(26, 26, 26)

        .Range("K29:K30").Merge
        .Range("K29").Value = ""
        .Range("K29").Font.Bold = True
        .Range("K29").Font.Size = 10
        .Range("K29").HorizontalAlignment = xlRight
        .Range("K29").VerticalAlignment = xlCenter
        .Range("K29").Interior.Color = RGB(255, 255, 180)

        ' Set row heights for the merged final tax row
        .Rows(29).RowHeight = 18
        .Rows(30).RowHeight = 18

        ' Apply borders to entire tax summary section with professional color
        .Range("H26:K30").Borders.LineStyle = xlContinuous
        .Range("H26:K30").Borders.Color = RGB(204, 204, 204)

        ' Setup automatic tax calculation formulas for summary section
        Call UpdateMultiItemTaxCalculations(ws)

        ' Amount in words conversion integrated into signature section

        On Error GoTo 0

        ' --- Signature Section with Merged Cells ---
        On Error Resume Next

        ' Row 31: Signature headers with merged cells
        .Range("A31:C31").Merge
        .Range("A31").Value = "Transporter"
        .Range("A31").Font.Bold = True
        .Range("A31").HorizontalAlignment = xlCenter
        .Range("A31").Interior.Color = RGB(220, 220, 220)

        .Range("D31:G31").Merge
        .Range("D31").Value = "Receiver"
        .Range("D31").Font.Bold = True
        .Range("D31").HorizontalAlignment = xlCenter
        .Range("D31").Interior.Color = RGB(220, 220, 220)

        .Range("H31:K31").Merge
        .Range("H31").Value = "Certified that the particulars given above are true and correct"
        .Range("H31").Font.Bold = True
        .Range("H31").Font.Size = 9
        .Range("H31").HorizontalAlignment = xlCenter
        .Range("H31").VerticalAlignment = xlCenter
        .Range("H31").WrapText = True
        .Range("H31").Interior.Color = RGB(220, 220, 220)

        ' Rows 32-33: Mobile Number Section (merged across 2 rows)
        .Range("A32:C33").Merge
        .Range("A32").Value = "Mobile No: ___________________"
        .Range("A32").Font.Size = 9
        .Range("A32").HorizontalAlignment = xlCenter
        .Range("A32").VerticalAlignment = xlCenter
        .Range("A32").Interior.Color = RGB(250, 250, 250)

        .Range("D32:G33").Merge
        .Range("D32").Value = "Mobile No: ___________________"
        .Range("D32").Font.Size = 9
        .Range("D32").HorizontalAlignment = xlCenter
        .Range("D32").VerticalAlignment = xlCenter
        .Range("D32").Interior.Color = RGB(250, 250, 250)

        .Range("H32:K33").Merge
        .Range("H32").Value = "Mobile No: ___________________"
        .Range("H32").Font.Size = 9
        .Range("H32").HorizontalAlignment = xlCenter
        .Range("H32").VerticalAlignment = xlCenter
        .Range("H32").Interior.Color = RGB(250, 250, 250)

        ' Rows 34-36: Signature Space Section (merged across 3 rows)
        .Range("A34:C36").Merge
        .Range("A34").Value = ""
        .Range("A34").Interior.Color = RGB(250, 250, 250)

        .Range("D34:G36").Merge
        .Range("D34").Value = ""
        .Range("D34").Interior.Color = RGB(250, 250, 250)

        .Range("H34:K36").Merge
        .Range("H34").Value = ""
        .Range("H34").Interior.Color = RGB(250, 250, 250)

        ' Row 37: Signature Labels
        .Range("A37:C37").Merge
        .Range("A37").Value = "Transporter's Signature"
        .Range("A37").Font.Bold = True
        .Range("A37").Font.Size = 9
        .Range("A37").HorizontalAlignment = xlCenter
        .Range("A37").Interior.Color = RGB(250, 250, 250)

        .Range("D37:G37").Merge
        .Range("D37").Value = "Receiver's Signature"
        .Range("D37").Font.Bold = True
        .Range("D37").Font.Size = 9
        .Range("D37").HorizontalAlignment = xlCenter
        .Range("D37").Interior.Color = RGB(250, 250, 250)

        .Range("H37:K37").Merge
        .Range("H37").Value = "Authorized Signatory"
        .Range("H37").Font.Bold = True
        .Range("H37").Font.Size = 9
        .Range("H37").HorizontalAlignment = xlCenter
        .Range("H37").Interior.Color = RGB(250, 250, 250)

        ' Apply borders to entire signature section with professional color
        .Range("A31:K37").Borders.LineStyle = xlContinuous
        .Range("A31:K37").Borders.Color = RGB(204, 204, 204)

        ' Set specific row height for row 31 to accommodate wrapped text
        .Rows(31).RowHeight = 35

        ' Set standard row height for remaining signature rows
        For i = 32 To 37
            .Rows(i).RowHeight = 20
        Next i
        On Error GoTo 0
    End With

    ' --- Final Formatting ---
    With ws
        On Error Resume Next
        ' Font settings moved to beginning of code to avoid overriding header fonts
        On Error GoTo 0

        ' Apply professional page setup
        On Error Resume Next
        With .PageSetup
            .Orientation = xlPortrait
            .Zoom = False
            .FitToPagesWide = 1
            .FitToPagesTall = 1
            .LeftMargin = Application.InchesToPoints(0.3)
            .RightMargin = Application.InchesToPoints(0.3)
            .TopMargin = Application.InchesToPoints(0.4)
            .BottomMargin = Application.InchesToPoints(0.4)
            .HeaderMargin = Application.InchesToPoints(0.2)
            .FooterMargin = Application.InchesToPoints(0.2)
            .CenterHorizontally = True
            .CenterVertically = False
        End With
        On Error GoTo 0

        ' Add a subtle border around the entire invoice
        On Error Resume Next
        With .Range("A1:K37")
            .Borders(xlEdgeLeft).LineStyle = xlContinuous
            .Borders(xlEdgeLeft).Weight = xlThick
            .Borders(xlEdgeRight).LineStyle = xlContinuous
            .Borders(xlEdgeRight).Weight = xlThick
            .Borders(xlEdgeTop).LineStyle = xlContinuous
            .Borders(xlEdgeTop).Weight = xlThick
            .Borders(xlEdgeBottom).LineStyle = xlContinuous
            .Borders(xlEdgeBottom).Weight = xlThick
        End With
        On Error GoTo 0
    End With

    ' Create professional buttons for invoice operations
    Call CreateInvoiceButtons(ws)

    ' Auto-populate invoice number and dates
    Call AutoPopulateInvoiceFields(ws)

    ' Set up worksheet change events for auto-population
    Call SetupWorksheetChangeEvents(ws)

    ' Set up worksheet change events for state code extraction
    Call SetupStateCodeChangeEvents(ws)

    ' Auto-fill consignee from receiver data
    Call AutoFillConsigneeFromReceiver(ws)

    ' Restore Excel alerts
    Application.DisplayAlerts = True

    MsgBox "GST TAX-INVOICE created successfully with professional buttons and auto-populated fields!", vbInformation
    Exit Sub

ErrorHandler:
    ' Restore Excel alerts even in case of error
    Application.DisplayAlerts = True
    MsgBox "An error occurred in CreateInvoiceSheet." & vbCrLf & _
           "Error Number: " & Err.Number & vbCrLf & _
           "Error Description: " & Err.Description, vbCritical, "Error"
    On Error GoTo 0
End Sub

Private Sub CreateInvoiceButtons(ws As Worksheet)
    ' Create professional buttons for the 7 button functions on the right side of the invoice
    On Error GoTo ErrorHandler

    Dim btn As Button
    Dim btnTop As Double
    Dim btnLeft As Double
    Dim btnWidth As Double
    Dim btnHeight As Double
    Dim btnSpacing As Double

    ' Remove any existing buttons first
    Call RemoveExistingButtons(ws)

    ' Button positioning and sizing (professional layout)
    btnLeft = ws.Columns("Q").Left + 5  ' Start at column Q with small margin
    btnWidth = 180  ' Professional button width
    btnHeight = 30  ' Professional button height
    btnSpacing = 8  ' Space between buttons
    btnTop = ws.Range("A8").Top  ' Start around row 8

    ' === CUSTOMER MANAGEMENT SECTION ===
    ' Add section header
    With ws.Range("Q6")
        .Value = "📋 INVOICE OPERATIONS"
        .Font.Bold = True
        .Font.Size = 11
        .Font.Color = RGB(47, 80, 97)
        .HorizontalAlignment = xlCenter
    End With

    ' Button 1: Save Customer to Warehouse
    Set btn = ws.Buttons.Add(btnLeft, btnTop, btnWidth, btnHeight)
    With btn
        .Name = "SaveCustomerBtn"
        .Caption = "Save Customer to Warehouse"
        .OnAction = "AddCustomerToWarehouseButton"
        .Font.Name = "Segoe UI"
        .Font.Size = 9
        .Font.Bold = True
    End With

    ' Button 2: Save Invoice Record
    btnTop = btnTop + btnHeight + btnSpacing
    Set btn = ws.Buttons.Add(btnLeft, btnTop, btnWidth, btnHeight)
    With btn
        .Name = "SaveInvoiceBtn"
        .Caption = "Save Invoice Record"
        .OnAction = "SaveInvoiceButton"
        .Font.Name = "Segoe UI"
        .Font.Size = 9
        .Font.Bold = True
    End With

    ' Button 3: New Invoice
    btnTop = btnTop + btnHeight + btnSpacing
    Set btn = ws.Buttons.Add(btnLeft, btnTop, btnWidth, btnHeight)
    With btn
        .Name = "NewInvoiceBtn"
        .Caption = "New Invoice"
        .OnAction = "NewInvoiceButton"
        .Font.Name = "Segoe UI"
        .Font.Size = 9
        .Font.Bold = True
    End With

    ' === ITEM MANAGEMENT SECTION ===
    btnTop = btnTop + btnHeight + btnSpacing + 15  ' Extra space for section
    With ws.Range("Q" & Application.WorksheetFunction.RoundUp((btnTop - ws.Range("A1").Top) / ws.Rows(1).Height, 0))
        .Value = "📝 ITEM MANAGEMENT"
        .Font.Bold = True
        .Font.Size = 11
        .Font.Color = RGB(47, 80, 97)
        .HorizontalAlignment = xlCenter
    End With
    btnTop = btnTop + 20  ' Space after header

    ' Button 4: Add New Item Row
    Set btn = ws.Buttons.Add(btnLeft, btnTop, btnWidth, btnHeight)
    With btn
        .Name = "AddItemBtn"
        .Caption = "Add New Item Row"
        .OnAction = "AddNewItemRowButton"
        .Font.Name = "Segoe UI"
        .Font.Size = 9
        .Font.Bold = True
    End With

    ' Button 5: Remove Last Row - REMOVED for cleaner interface
    ' This functionality has been removed to streamline the invoice interface

    ' === PRINT & EXPORT SECTION ===
    btnTop = btnTop + btnHeight + btnSpacing + 15  ' Extra space for section
    With ws.Range("Q" & Application.WorksheetFunction.RoundUp((btnTop - ws.Range("A1").Top) / ws.Rows(1).Height, 0))
        .Value = "🖨️ PRINT & EXPORT"
        .Font.Bold = True
        .Font.Size = 11
        .Font.Color = RGB(47, 80, 97)
        .HorizontalAlignment = xlCenter
    End With
    btnTop = btnTop + 20  ' Space after header

    ' Button 6: Export as PDF
    Set btn = ws.Buttons.Add(btnLeft, btnTop, btnWidth, btnHeight)
    With btn
        .Name = "ExportPDFBtn"
        .Caption = "Export as PDF"
        .OnAction = "PrintAsPDFButton"
        .Font.Name = "Segoe UI"
        .Font.Size = 9
        .Font.Bold = True
    End With

    ' Button 7: Print Invoice
    btnTop = btnTop + btnHeight + btnSpacing
    Set btn = ws.Buttons.Add(btnLeft, btnTop, btnWidth, btnHeight)
    With btn
        .Name = "PrintInvoiceBtn"
        .Caption = "Print Invoice"
        .OnAction = "PrintButton"
        .Font.Name = "Segoe UI"
        .Font.Size = 9
        .Font.Bold = True
    End With

    ' Add professional footer note
    btnTop = btnTop + btnHeight + 15
    With ws.Range("Q" & Application.WorksheetFunction.RoundUp((btnTop - ws.Range("A1").Top) / ws.Rows(1).Height, 0))
        .Value = "💡 Click buttons for quick operations"
        .Font.Size = 8
        .Font.Italic = True
        .Font.Color = RGB(100, 100, 100)
        .HorizontalAlignment = xlCenter
    End With

    Exit Sub

ErrorHandler:
    MsgBox "Error creating invoice buttons: " & Err.Description, vbCritical, "Button Creation Error"
End Sub

Private Sub RemoveExistingButtons(ws As Worksheet)
    ' Remove any existing buttons to prevent duplicates
    On Error Resume Next

    Dim btn As Button
    Dim i As Integer

    ' Remove buttons by name (safer approach)
    Dim buttonNames As Variant
    buttonNames = Array("SaveCustomerBtn", "SaveInvoiceBtn", "ClearDetailsBtn", _
                       "AddItemBtn", "RemoveRowBtn", "ExportPDFBtn", "PrintInvoiceBtn")

    For i = 0 To UBound(buttonNames)
        Set btn = Nothing
        Set btn = ws.Buttons(buttonNames(i))
        If Not btn Is Nothing Then
            btn.Delete
        End If
    Next i

    ' Also remove any buttons in the Q column area (fallback)
    For Each btn In ws.Buttons
        If btn.Left >= ws.Columns("Q").Left And btn.Left <= ws.Columns("S").Left Then
            btn.Delete
        End If
    Next btn

    On Error GoTo 0
End Sub

' Helper function to create header rows safely
Private Sub CreateHeaderRow(ws As Worksheet, rowNum As Integer, rangeAddr As String, text As String, fontSize As Integer, isBold As Boolean, backColor As Long, fontColor As Long, rowHeight As Integer)
    On Error Resume Next

    ' Set the text in the first cell
    ws.Range(rangeAddr).Cells(1, 1).Value = CleanText(text)

    ' Apply formatting to the entire range
    With ws.Range(rangeAddr)
        .Font.Bold = isBold
        .Font.Size = fontSize
        .Font.Color = fontColor
        .Interior.Color = backColor
        .HorizontalAlignment = xlCenter
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlMedium

        ' Try to merge - if it fails, continue anyway
        .Merge
        If Err.Number <> 0 Then Err.Clear
    End With

    ' Set row height
    ws.Rows(rowNum).RowHeight = rowHeight

    On Error GoTo 0
End Sub

Private Sub AutoPopulateInvoiceFields(ws As Worksheet)
    ' Auto-populate invoice number and dates with full manual override capability
    ' ALL auto-populated values can be manually edited by users
    On Error GoTo ErrorHandler

    ' Auto-populate Invoice Number (Row 7, Column C)
    Dim nextInvoiceNumber As String
    nextInvoiceNumber = GetNextInvoiceNumber()

    With ws.Range("C7")
        .Value = nextInvoiceNumber
        .Font.Bold = True
        .Font.Color = RGB(220, 20, 60)  ' Red color for user input
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        ' Allow manual editing - no validation restrictions
    End With

    ' Auto-populate Invoice Date (Row 8, Column C)
    With ws.Range("C8")
        .Value = Format(Date, "dd/mm/yyyy")
        .Font.Bold = True
        .HorizontalAlignment = xlLeft
        ' Allow manual editing - no validation restrictions
    End With

    ' Auto-populate Date of Supply (Row 9, Columns F & G)
    With ws.Range("F9")
        .Value = Format(Date, "dd/mm/yyyy")
        .Font.Bold = True
        .HorizontalAlignment = xlLeft
        ' Allow manual editing - no validation restrictions
    End With

    With ws.Range("G9")
        .Value = Format(Date, "dd/mm/yyyy")
        .Font.Bold = True
        .HorizontalAlignment = xlLeft
        ' Allow manual editing - no validation restrictions
    End With

    ' Set fixed State Code (Row 10, Column C) for Andhra Pradesh
    With ws.Range("C10")
        .Value = "37"
        .Font.Bold = True
        .Interior.Color = RGB(245, 245, 245)  ' Light grey background
        .Font.Color = RGB(26, 26, 26)  ' Dark text
        .HorizontalAlignment = xlLeft
        ' No validation - fixed value
    End With

    Exit Sub

ErrorHandler:
    ' If auto-population fails, set default values
    ws.Range("C7").Value = "INV-" & Year(Date) & "-001"
    ws.Range("C8").Value = Format(Date, "dd/mm/yyyy")
    ws.Range("F9").Value = Format(Date, "dd/mm/yyyy")
    ws.Range("G9").Value = Format(Date, "dd/mm/yyyy")
End Sub

Private Sub SetupWorksheetChangeEvents(ws As Worksheet)
    ' Set up change monitoring for customer dropdown auto-population
    ' Since we're in a module, we'll use a different approach
    On Error Resume Next

    ' Note: Proper header "Details of Receiver (Billed to)" is created by CreateHeaderRow function
    ' No additional placeholder text needed - header is already properly formatted

    On Error GoTo 0
End Sub

Private Sub SetupStateCodeChangeEvents(ws As Worksheet)
    ' Simple state code setup - no automatic extraction needed
    ' State code dropdowns will show simple numeric codes only
    On Error GoTo 0
End Sub



Private Sub AutoFillConsigneeFromReceiver(ws As Worksheet)
    ' Automatically copy all receiver data to consignee fields
    On Error GoTo ErrorHandler

    With ws
        ' Copy Name from Receiver (C12:F12) to Consignee (I12:K12)
        .Range("I12").Value = .Range("C12").Value

        ' Copy Address from Receiver (C13:F13) to Consignee (I13:K13)
        .Range("I13").Value = .Range("C13").Value

        ' Copy GSTIN from Receiver (C14:F14) to Consignee (I14:K14)
        .Range("I14").Value = .Range("C14").Value

        ' Copy State from Receiver (C15:F15) to Consignee (I15:K15)
        .Range("I15").Value = .Range("C15").Value

        ' Copy State Code from Receiver (C16:F16) to Consignee (I16:K16)
        .Range("I16").Value = .Range("C16").Value

        ' Format consignee fields for manual editing (use default black font)
        .Range("I12:K12").Font.Color = RGB(26, 26, 26)  ' Standard black font
        .Range("I13:K13").Font.Color = RGB(26, 26, 26)  ' Standard black font
        .Range("I14:K14").Font.Color = RGB(26, 26, 26)  ' Standard black font
        .Range("I15:K15").Font.Color = RGB(26, 26, 26)  ' Standard black font
        .Range("I16:K16").Font.Color = RGB(26, 26, 26)  ' Standard black font
    End With

    Exit Sub

ErrorHandler:
    ' If auto-fill fails, continue silently
    On Error GoTo 0
End Sub



' Function to get state code based on state name
Private Function GetStateCode(stateName As String) As String
    ' Return corresponding state code for given state name
    Dim stateList As Variant
    Dim stateCodeList As Variant
    Dim i As Integer

    ' Define state mappings (same as in warehouse sheet)
    stateList = Array("Andhra Pradesh", "Telangana", "Karnataka", "Tamil Nadu", "Kerala", "Maharashtra", "Gujarat", "Rajasthan", "Delhi", "Punjab")
    stateCodeList = Array("37", "36", "29", "33", "32", "27", "24", "08", "07", "03")

    ' Find matching state and return code
    For i = 0 To UBound(stateList)
        If UCase(Trim(stateName)) = UCase(Trim(stateList(i))) Then
            GetStateCode = stateCodeList(i)
            Exit Function
        End If
    Next i

    ' If no match found, return empty string
    GetStateCode = ""
End Function





' Helper function to get IGST rate for a specific HSN code
Private Function GetHSNIGSTRate(hsnCode As String, warehouseWs As Worksheet) As String
    ' Search for HSN code in warehouse sheet and return corresponding IGST rate
    Dim lastRow As Long
    Dim i As Long
    Dim currentHSN As String

    On Error GoTo ErrorHandler

    ' Find last row with HSN data (Column A)
    lastRow = warehouseWs.Cells(warehouseWs.Rows.Count, "A").End(xlUp).Row

    ' Search for matching HSN code (starting from row 2, skipping header)
    For i = 2 To lastRow
        currentHSN = Trim(warehouseWs.Cells(i, "A").Value)  ' Column A = HSN Code

        If UCase(currentHSN) = UCase(hsnCode) Then
            ' Found matching HSN - return IGST rate from Column E
            GetHSNIGSTRate = Trim(warehouseWs.Cells(i, "E").Value)
            Exit Function
        End If
    Next i

    ' No match found
    GetHSNIGSTRate = ""
    Exit Function

ErrorHandler:
    GetHSNIGSTRate = ""
End Function

' Function to verify that all validations allow manual override
Sub VerifyValidationSettings()
    ' Display current validation settings to confirm manual editing is enabled
    Dim ws As Worksheet
    Dim message As String

    On Error GoTo ErrorHandler

    Set ws = ThisWorkbook.Worksheets("GST_Tax_Invoice_for_interstate")

    message = "VALIDATION SETTINGS VERIFICATION:" & vbCrLf & vbCrLf
    message = message & "✅ ALL FIELDS SUPPORT MANUAL EDITING:" & vbCrLf & vbCrLf

    message = message & "📝 DROPDOWN + MANUAL ENTRY FIELDS:" & vbCrLf
    message = message & "• Customer Name (C12) - Dropdown + Manual" & vbCrLf
    message = message & "• Receiver State (C15) - Dropdown + Manual" & vbCrLf
    message = message & "• Consignee State (I15) - Dropdown + Manual" & vbCrLf
    message = message & "• HSN Code (C18:C21) - Dropdown + Manual" & vbCrLf
    message = message & "• UOM (E18:E21) - Dropdown + Manual" & vbCrLf
    message = message & "• Transport Mode (F7) - Dropdown + Manual" & vbCrLf & vbCrLf

    message = message & "🔓 FULLY EDITABLE FIELDS:" & vbCrLf
    message = message & "• Invoice Number (C7) - Auto + Manual Override" & vbCrLf
    message = message & "• Invoice Date (C8) - Auto + Manual Override" & vbCrLf
    message = message & "• Date of Supply (F9, G9) - Auto + Manual Override" & vbCrLf
    message = message & "• State Code (C10) - Fixed + Manual Override" & vbCrLf
    message = message & "• All Address/GSTIN fields - Fully Manual" & vbCrLf
    message = message & "• All Item details - Fully Manual" & vbCrLf & vbCrLf

    message = message & "🎯 KEY FEATURES:" & vbCrLf
    message = message & "• No restrictive validations (xlValidAlertStop removed)" & vbCrLf
    message = message & "• ShowError = False for all dropdowns" & vbCrLf
    message = message & "• Users can override ANY auto-populated value" & vbCrLf
    message = message & "• Dropdown suggestions + free text entry" & vbCrLf & vbCrLf

    message = message & "💡 All validation requirements have been successfully implemented!"

    MsgBox message, vbInformation, "Validation Settings - All Clear ✅"
    Exit Sub

ErrorHandler:
    MsgBox "Error verifying validation settings: " & Err.Description, vbCritical, "Verification Error"
End Sub







' Helper function to clean text and remove problematic characters
Private Function CleanText(inputText As String) As String
    Dim cleanedText As String
    Dim i As Integer

    cleanedText = inputText

    ' Remove any question marks that might appear due to encoding issues
    cleanedText = Replace(cleanedText, "?", "")

    ' Remove any other problematic characters
    cleanedText = Replace(cleanedText, Chr(63), "") ' ASCII 63 is question mark

    ' Trim extra spaces
    cleanedText = Trim(cleanedText)

    ' Replace multiple spaces with single space
    Do While InStr(cleanedText, "  ") > 0
        cleanedText = Replace(cleanedText, "  ", " ")
    Loop

    CleanText = cleanedText
End Function
Private Function NumberToWords(ByVal MyNumber)
    Dim Rupees, Paise, Temp
    Dim DecimalPlace, Count
    ReDim Place(9) As String
    Place(2) = " Thousand "
    Place(3) = " Lakh "
    Place(4) = " Crore "

    MyNumber = Trim(Str(MyNumber))
    DecimalPlace = InStr(MyNumber, ".")

    If DecimalPlace > 0 Then
        Paise = ConvertTens(Left(Mid(MyNumber, DecimalPlace + 1) & "00", 2))
        MyNumber = Trim(Left(MyNumber, DecimalPlace - 1))
    End If

    Count = 1
    Do While MyNumber <> ""
        Select Case Count
            Case 1
                Temp = ConvertHundreds(Right(MyNumber, 3))
                If Len(MyNumber) > 3 Then
                    MyNumber = Left(MyNumber, Len(MyNumber) - 3)
                Else
                    MyNumber = ""
                End If
            Case 2
                Temp = ConvertTens(Right(MyNumber, 2))
                If Len(MyNumber) > 2 Then
                    MyNumber = Left(MyNumber, Len(MyNumber) - 2)
                Else
                    MyNumber = ""
                End If
            Case Else
                Temp = ConvertTens(Right(MyNumber, 2))
                If Len(MyNumber) > 2 Then
                    MyNumber = Left(MyNumber, Len(MyNumber) - 2)
                Else
                    MyNumber = ""
                End If
        End Select

        If Temp <> "" Then Rupees = Temp & Place(Count) & Rupees
        Count = Count + 1
    Loop

    Select Case Rupees
        Case ""
            Rupees = "Zero Rupees"
        Case "One"
            Rupees = "One Rupee"
        Case Else
            Rupees = Rupees & " Rupees"
    End Select

    If Paise <> "" Then
        Select Case Paise
            Case "One"
                Paise = " and One Paisa"
            Case Else
                Paise = " and " & Paise & " Paise"
        End Select
    End If

    NumberToWords = CleanText(Rupees & Paise & " Only")
End Function

Private Function ConvertHundreds(ByVal MyNumber)
    Dim Result As String

    ' Exit if there is nothing to convert
    If Val(MyNumber) = 0 Then Exit Function

    ' Append leading zeros to number
    MyNumber = Right("000" & MyNumber, 3)

    ' Do we have a hundreds place digit to convert?
    If Left(MyNumber, 1) <> "0" Then
        Result = ConvertDigit(Left(MyNumber, 1)) & " Hundred "
    End If

    ' Do we have a tens place digit to convert?
    If Mid(MyNumber, 2, 1) <> "0" Then
        Result = Result & ConvertTens(Mid(MyNumber, 2))
    Else
        ' If not, then convert the ones place digit
        Result = Result & ConvertDigit(Mid(MyNumber, 3))
    End If

    ConvertHundreds = Trim(Result)
End Function

Private Function ConvertTens(ByVal MyTens)
    Dim Result As String

    ' Is value between 10 and 19?
    If Val(Left(MyTens, 1)) = 1 Then
        Select Case Val(MyTens)
            Case 10: Result = "Ten"
            Case 11: Result = "Eleven"
            Case 12: Result = "Twelve"
            Case 13: Result = "Thirteen"
            Case 14: Result = "Fourteen"
            Case 15: Result = "Fifteen"
            Case 16: Result = "Sixteen"
            Case 17: Result = "Seventeen"
            Case 18: Result = "Eighteen"
            Case 19: Result = "Nineteen"
            Case Else
        End Select
    Else
        ' .. otherwise it's between 20 and 99
        Select Case Val(Left(MyTens, 1))
            Case 2: Result = "Twenty "
            Case 3: Result = "Thirty "
            Case 4: Result = "Forty "
            Case 5: Result = "Fifty "
            Case 6: Result = "Sixty "
            Case 7: Result = "Seventy "
            Case 8: Result = "Eighty "
            Case 9: Result = "Ninety "
            Case Else
        End Select

        ' Convert ones place digit
        Result = Result & ConvertDigit(Right(MyTens, 1))
    End If

    ConvertTens = Result
End Function

Private Function ConvertDigit(ByVal MyDigit)
    Select Case Val(MyDigit)
        Case 1: ConvertDigit = "One"
        Case 2: ConvertDigit = "Two"
        Case 3: ConvertDigit = "Three"
        Case 4: ConvertDigit = "Four"
        Case 5: ConvertDigit = "Five"
        Case 6: ConvertDigit = "Six"
        Case 7: ConvertDigit = "Seven"
        Case 8: ConvertDigit = "Eight"
        Case 9: ConvertDigit = "Nine"
        Case Else: ConvertDigit = ""
    End Select
End Function

' ===== SUPPORTING WORKSHEETS CREATION =====

Private Sub CreateSupportingWorksheets()
    ' Create all supporting worksheets for the enhanced GST invoice system
    On Error GoTo ErrorHandler

    Call CreateMasterSheet
    Call CreateWarehouseSheet

    MsgBox "All supporting worksheets created successfully!", vbInformation, "Setup Complete"
    Exit Sub

ErrorHandler:
    MsgBox "Error creating supporting worksheets: " & Err.Description, vbCritical
End Sub





Private Sub CreateMasterSheet()
    Dim ws As Worksheet

    On Error Resume Next
    Set ws = ThisWorkbook.Sheets("Master")
    If Not ws Is Nothing Then
        Application.DisplayAlerts = False
        ws.Delete
        Application.DisplayAlerts = True
    End If
    On Error GoTo 0

    Set ws = ThisWorkbook.Sheets.Add(After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.Count))
    ws.Name = "Master"

    With ws
        ' ===== GST INVOICE RECORDS FOR AUDIT & RETURN FILING (A1:P1) =====
        ' GST-compliant headers for complete invoice records
        .Range("A1").Value = "Invoice_Number"
        .Range("B1").Value = "Invoice_Date"
        .Range("C1").Value = "Customer_Name"
        .Range("D1").Value = "Customer_GSTIN"
        .Range("E1").Value = "Customer_State"
        .Range("F1").Value = "Customer_State_Code"
        .Range("G1").Value = "Total_Taxable_Value"
        .Range("H1").Value = "IGST_Rate"
        .Range("I1").Value = "IGST_Amount"
        .Range("J1").Value = "Total_Tax_Amount"
        .Range("K1").Value = "Total_Invoice_Value"
        .Range("L1").Value = "HSN_Codes"
        .Range("M1").Value = "Item_Description"
        .Range("N1").Value = "Quantity"
        .Range("O1").Value = "UOM"
        .Range("P1").Value = "Date_Created"

        ' Format GST audit headers
        .Range("A1:P1").Font.Bold = True
        .Range("A1:P1").Interior.Color = RGB(47, 80, 97)
        .Range("A1:P1").Font.Color = RGB(255, 255, 255)
        .Range("A1:P1").HorizontalAlignment = xlCenter
        .Range("A1:P1").WrapText = True
        .Rows(1).RowHeight = 30

        ' Add borders to header
        .Range("A1:P1").Borders.LineStyle = xlContinuous
        .Range("A1:P1").Borders.Color = RGB(204, 204, 204)

        ' Auto-fit columns for better visibility
        .Columns.AutoFit

        ' Set specific column widths for GST data
        .Columns("A").ColumnWidth = 15  ' Invoice Number
        .Columns("B").ColumnWidth = 12  ' Invoice Date
        .Columns("C").ColumnWidth = 20  ' Customer Name
        .Columns("D").ColumnWidth = 18  ' Customer GSTIN
        .Columns("E").ColumnWidth = 15  ' Customer State
        .Columns("F").ColumnWidth = 12  ' State Code
        .Columns("G").ColumnWidth = 15  ' Taxable Value
        .Columns("H").ColumnWidth = 10  ' IGST Rate
        .Columns("I").ColumnWidth = 12  ' IGST Amount
        .Columns("J").ColumnWidth = 12  ' Total Tax
        .Columns("K").ColumnWidth = 15  ' Invoice Value
        .Columns("L").ColumnWidth = 15  ' HSN Codes
        .Columns("M").ColumnWidth = 25  ' Item Description
        .Columns("N").ColumnWidth = 10  ' Quantity
        .Columns("O").ColumnWidth = 8   ' UOM
        .Columns("P").ColumnWidth = 12  ' Date Created

        ' Note: Explanatory note removed to prevent data layout interference
    End With
End Sub



Private Sub CreateWarehouseSheet()
    Dim ws As Worksheet
    Dim i As Integer, j As Integer

    On Error Resume Next
    Set ws = ThisWorkbook.Sheets("warehouse")
    If Not ws Is Nothing Then
        Application.DisplayAlerts = False
        ws.Delete
        Application.DisplayAlerts = True
    End If
    On Error GoTo 0

    Set ws = ThisWorkbook.Sheets.Add(After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.Count))
    ws.Name = "warehouse"

    With ws
        ' ===== SECTION 1: HSN/SAC DATA (Columns A-E) =====
        ' HSN headers
        .Range("A1").Value = "HSN_Code"
        .Range("B1").Value = "Description"
        .Range("C1").Value = "CGST_Rate"
        .Range("D1").Value = "SGST_Rate"
        .Range("E1").Value = "IGST_Rate"

        ' Format HSN headers
        .Range("A1:E1").Font.Bold = True
        .Range("A1:E1").Interior.Color = RGB(47, 80, 97)
        .Range("A1:E1").Font.Color = RGB(255, 255, 255)
        .Range("A1:E1").HorizontalAlignment = xlCenter

        ' Add sample HSN data
        Dim hsnData As Variant
        hsnData = Array( _
            Array("4403", "Casuarina Wood", 6, 6, 12), _
            Array("4407", "Sawn Wood", 6, 6, 12), _
            Array("4409", "Wood Flooring", 9, 9, 18), _
            Array("2501", "Salt", 2.5, 2.5, 5), _
            Array("1006", "Rice", 2.5, 2.5, 5), _
            Array("7208", "Steel Sheets", 9, 9, 18), _
            Array("8471", "Computer", 9, 9, 18), _
            Array("8517", "Mobile Phone", 9, 9, 18) _
        )

        For i = 0 To UBound(hsnData)
            For j = 0 To UBound(hsnData(i))
                .Cells(i + 2, j + 1).Value = hsnData(i)(j)  ' Starting at row 2, column A (1)
            Next j
        Next i

        ' ===== SECTION 2: VALIDATION LISTS =====
        ' UOM List (Column G)
        .Range("G1").Value = "UOM_List"
        .Range("G1").Font.Bold = True
        .Range("G1").Interior.Color = RGB(47, 80, 97)
        .Range("G1").Font.Color = RGB(255, 255, 255)

        Dim uomList As Variant
        uomList = Array("NOS", "KG", "MT", "CBM", "SQM", "LTR", "PCS", "BOX", "SET", "PAIR")
        For i = 0 To UBound(uomList)
            .Cells(i + 2, 7).Value = uomList(i)
        Next i

        ' Transport Mode List (Column H)
        .Range("H1").Value = "Transport_Mode_List"
        .Range("H1").Font.Bold = True
        .Range("H1").Interior.Color = RGB(47, 80, 97)
        .Range("H1").Font.Color = RGB(255, 255, 255)

        Dim transportList As Variant
        transportList = Array("By Lorry", "By Train", "By Air", "By Ship", "By Hand", "Courier", "Self Transport")
        For i = 0 To UBound(transportList)
            .Cells(i + 2, 8).Value = transportList(i)
        Next i

        ' State List (Column J)
        .Range("J1").Value = "State_List"
        .Range("J1").Font.Bold = True
        .Range("J1").Interior.Color = RGB(47, 80, 97)
        .Range("J1").Font.Color = RGB(255, 255, 255)

        Dim stateList As Variant
        stateList = Array("Andhra Pradesh", "Telangana", "Karnataka", "Tamil Nadu", "Kerala", "Maharashtra", "Gujarat", "Rajasthan", "Delhi", "Punjab")
        For i = 0 To UBound(stateList)
            .Cells(i + 2, 10).Value = stateList(i)
        Next i

        ' State Code List (Column K)
        .Range("K1").Value = "State_Code_List"
        .Range("K1").Font.Bold = True
        .Range("K1").Interior.Color = RGB(47, 80, 97)
        .Range("K1").Font.Color = RGB(255, 255, 255)

        Dim stateCodeList As Variant
        stateCodeList = Array("37", "36", "29", "33", "32", "27", "24", "08", "07", "03")
        For i = 0 To UBound(stateCodeList)
            .Cells(i + 2, 11).Value = stateCodeList(i)
        Next i

        ' ===== SECTION 3: CUSTOMER MASTER DATA (Columns M-T) =====
        ' Customer headers (restored to original positions)
        .Range("M1").Value = "Customer_Name"
        .Range("N1").Value = "Address_Line1"
        .Range("O1").Value = "State"
        .Range("P1").Value = "State_Code"
        .Range("Q1").Value = "GSTIN"
        .Range("R1").Value = "Phone"
        .Range("S1").Value = "Email"
        .Range("T1").Value = "Contact_Person"

        ' Format customer headers
        .Range("M1:T1").Font.Bold = True
        .Range("M1:T1").Interior.Color = RGB(47, 80, 97)
        .Range("M1:T1").Font.Color = RGB(255, 255, 255)
        .Range("M1:T1").HorizontalAlignment = xlCenter

        ' Add sample customer data (simplified structure)
        Dim customerData As Variant
        customerData = Array( _
            Array("ABC Industries Ltd", "123 Industrial Area, Sector 15, Tirupati", "Andhra Pradesh", "37", "37ABCDE1234F1Z5", "9876543210", "<EMAIL>", "Mr. Sharma"), _
            Array("XYZ Trading Co", "456 Market Street, Near Bus Stand, Vijayawada", "Andhra Pradesh", "37", "37XYZAB5678G2H6", "9876543211", "<EMAIL>", "Ms. Patel"), _
            Array("PQR Enterprises", "789 Commercial Complex, Phase 2, Visakhapatnam", "Andhra Pradesh", "37", "37PQRST9012I3J7", "9876543212", "<EMAIL>", "Mr. Kumar") _
        )

        For i = 0 To UBound(customerData)
            For j = 0 To UBound(customerData(i))
                .Cells(i + 2, j + 13).Value = customerData(i)(j)  ' Starting at column M (13)
            Next j
        Next i

        ' Auto-fit columns
        .Columns.AutoFit

        ' Add borders to all sections
        ' HSN data borders
        .Range("A1:E" & UBound(hsnData) + 2).Borders.LineStyle = xlContinuous
        .Range("A1:E" & UBound(hsnData) + 2).Borders.Color = RGB(204, 204, 204)

        ' Validation lists borders
        .Range("G1:G" & UBound(uomList) + 2).Borders.LineStyle = xlContinuous
        .Range("H1:H" & UBound(transportList) + 2).Borders.LineStyle = xlContinuous
        .Range("J1:J" & UBound(stateList) + 2).Borders.LineStyle = xlContinuous
        .Range("K1:K" & UBound(stateCodeList) + 2).Borders.LineStyle = xlContinuous

        ' Customer data borders
        .Range("M1:T" & UBound(customerData) + 2).Borders.LineStyle = xlContinuous
        .Range("M1:T" & UBound(customerData) + 2).Borders.Color = RGB(204, 204, 204)
    End With
End Sub

' ===== AUTO-INVOICE NUMBERING SYSTEM =====

Private Function GetNextInvoiceNumber() As String
    Dim masterWs As Worksheet
    Dim currentYear As Integer
    Dim counter As Integer
    Dim newInvoiceNumber As String
    Dim lastRow As Long
    Dim i As Long
    Dim maxCounter As Integer
    Dim invoiceNum As String

    On Error GoTo ErrorHandler

    ' Ensure supporting worksheets exist
    Call EnsureAllSupportingWorksheetsExist

    ' Get or create Master sheet
    Set masterWs = GetOrCreateWorksheet("Master")

    currentYear = Year(Date)
    maxCounter = 0

    ' Find the highest counter for the current year by examining existing invoice records
    lastRow = masterWs.Cells(masterWs.Rows.Count, "A").End(xlUp).Row

    If lastRow > 1 Then ' If there are invoice records
        For i = 2 To lastRow ' Start from row 2 (after header)
            invoiceNum = Trim(masterWs.Cells(i, "A").Value)
            If invoiceNum <> "" And InStr(invoiceNum, "INV-" & currentYear & "-") = 1 Then
                ' Extract counter from invoice number (format: INV-YYYY-NNN)
                maxCounter = Application.WorksheetFunction.Max(maxCounter, Val(Right(invoiceNum, 3)))
            End If
        Next i
    End If

    ' Set next counter
    counter = maxCounter + 1

    ' Generate new invoice number
    newInvoiceNumber = "INV-" & currentYear & "-" & Format(counter, "000")

    GetNextInvoiceNumber = newInvoiceNumber
    Exit Function

ErrorHandler:
    GetNextInvoiceNumber = "INV-" & Year(Date) & "-001"
End Function

Private Function GetCurrentInvoiceNumber() As String
    Dim masterWs As Worksheet
    Dim lastRow As Long
    Dim currentYear As Integer
    Dim maxCounter As Integer
    Dim i As Long
    Dim invoiceNum As String

    On Error GoTo ErrorHandler

    ' Ensure supporting worksheets exist
    Call EnsureAllSupportingWorksheetsExist

    Set masterWs = GetOrCreateWorksheet("Master")
    currentYear = Year(Date)
    maxCounter = 0

    If masterWs Is Nothing Then
        GetCurrentInvoiceNumber = "INV-" & currentYear & "-001"
        Exit Function
    End If

    ' Find the highest counter for the current year
    lastRow = masterWs.Cells(masterWs.Rows.Count, "A").End(xlUp).Row

    If lastRow > 1 Then ' If there are invoice records
        For i = 2 To lastRow ' Start from row 2 (after header)
            invoiceNum = Trim(masterWs.Cells(i, "A").Value)
            If invoiceNum <> "" And InStr(invoiceNum, "INV-" & currentYear & "-") = 1 Then
                ' Extract counter from invoice number (format: INV-YYYY-NNN)
                maxCounter = Application.WorksheetFunction.Max(maxCounter, Val(Right(invoiceNum, 3)))
            End If
        Next i
    End If

    If maxCounter = 0 Then
        GetCurrentInvoiceNumber = "INV-" & currentYear & "-001"
    Else
        GetCurrentInvoiceNumber = "INV-" & currentYear & "-" & Format(maxCounter, "000")
    End If
    Exit Function

ErrorHandler:
    GetCurrentInvoiceNumber = "INV-" & Year(Date) & "-001"
End Function

Private Sub ResetInvoiceCounter()
    Dim response As VbMsgBoxResult

    response = MsgBox("WARNING: This will clear all invoice records from the Master sheet!" & vbCrLf & vbCrLf & _
                     "The invoice counter is now based on existing records in the Master sheet." & vbCrLf & _
                     "To reset numbering, you would need to clear the Master sheet." & vbCrLf & vbCrLf & _
                     "Are you sure you want to proceed?", vbYesNo + vbCritical, "Reset Invoice Counter")

    If response = vbYes Then
        Dim masterWs As Worksheet
        Set masterWs = GetOrCreateWorksheet("Master")

        If Not masterWs Is Nothing Then
            ' Clear all invoice records (keep only the header row)
            Dim lastRow As Long
            lastRow = masterWs.Cells(masterWs.Rows.Count, "A").End(xlUp).Row
            If lastRow > 1 Then
                masterWs.Range("A2:P" & lastRow).ClearContents
            End If
            MsgBox "All invoice records cleared! Next invoice will be INV-" & Year(Date) & "-001", vbInformation, "Reset Complete"
        Else
            MsgBox "Master sheet not found!", vbExclamation
        End If
    End If
End Sub

' ===== DYNAMIC DATE HANDLING SYSTEM =====





' ▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓
' 🧮 TAX CALCULATION FUNCTIONS
' ▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓

Private Sub SetupTaxCalculationFormulas(ws As Worksheet)
    ' Set up formulas for automatic tax calculations in the item table
    On Error Resume Next

    With ws
        ' For row 18 (first item row), set up formulas
        ' Column G (Amount) = Quantity * Rate
        .Range("G18").Formula = "=IF(AND(D18<>"""",F18<>""""),D18*F18,"""")"

        ' Column H (Taxable Value) = Amount (same as Amount for simplicity)
        .Range("H18").Formula = "=IF(G18<>"""",G18,"""")"

        ' Column I (IGST Rate) - VLOOKUP formula to get tax rate from HSN data
        .Range("I18").Formula = "=VLOOKUP(C18, warehouse!A:E, 5, FALSE)"

        ' Column J (IGST Amount) = Taxable Value * IGST Rate / 100
        .Range("J18").Formula = "=IF(AND(H18<>"""",I18<>""""),H18*I18/100,"""")"

        ' Column K (Total Amount) = Taxable Value + IGST Amount
        .Range("K18").Formula = "=IF(AND(H18<>"""",J18<>""""),H18+J18,"""")"

        ' Format the formula cells
        .Range("G18:K18").NumberFormat = "0.00"
        .Range("I18").NumberFormat = "0.00"
    End With

    On Error GoTo 0
End Sub

Private Sub UpdateTaxCalculations(ws As Worksheet)
    ' Update tax calculations in the summary section
    On Error Resume Next

    With ws
        ' Row 23: Total Amount Before Tax (sum of taxable values)
        .Range("K23").Formula = "=IF(H18<>"""",H18,0)"

        ' Row 24: CGST (0 for interstate)
        .Range("K24").Value = 0

        ' Row 25: SGST (0 for interstate)
        .Range("K25").Value = 0

        ' Row 26: IGST (sum of IGST amounts)
        .Range("K26").Formula = "=IF(J18<>"""",J18,0)"

        ' Row 27: CESS (0 by default)
        .Range("K27").Value = 0

        ' Row 28: Total Tax
        .Range("K28").Formula = "=K24+K25+K26+K27"

        ' Row 29-30: Total Amount After Tax
        .Range("K29").Formula = "=K23+K28"
        .Range("K30").Formula = "=K29"

        ' Format all calculation cells
        .Range("K23:K30").NumberFormat = "#,##0.00"
    End With

    On Error GoTo 0
End Sub

Private Function CalculateIGSTAmount(taxableValue As Double, igstRate As Double) As Double
    If taxableValue > 0 And igstRate > 0 Then
        CalculateIGSTAmount = taxableValue * igstRate / 100
    Else
        CalculateIGSTAmount = 0
    End If
End Function

Private Function CalculateTotalAmount(taxableValue As Double, igstAmount As Double) As Double
    CalculateTotalAmount = taxableValue + igstAmount
End Function

Private Sub RecalculateAllTaxes()
    ' Recalculate all tax formulas in the invoice
    Dim ws As Worksheet

    On Error GoTo ErrorHandler

    Set ws = GetOrCreateWorksheet("GST_Tax_Invoice_for_interstate")

    If ws Is Nothing Then
        MsgBox "Invoice sheet not found!", vbExclamation
        Exit Sub
    End If

    Call SetupTaxCalculationFormulas(ws)
    Call UpdateTaxCalculations(ws)

    MsgBox "Tax calculations updated successfully!", vbInformation
    Exit Sub

ErrorHandler:
    MsgBox "Error recalculating taxes: " & Err.Description, vbCritical
End Sub

' ===== MULTI-ITEM SUPPORT SYSTEM =====

Private Sub AddNewItemRow()
    Dim ws As Worksheet
    Dim lastItemRow As Long
    Dim newRowNum As Long
    Dim i As Integer

    On Error GoTo ErrorHandler

    Set ws = GetOrCreateWorksheet("GST_Tax_Invoice_for_interstate")

    If ws Is Nothing Then
        MsgBox "Invoice sheet not found!", vbExclamation
        Exit Sub
    End If

    ' Find the last item row (starts from row 18)
    lastItemRow = 18
    Do While ws.Cells(lastItemRow + 1, 1).Value <> "" Or lastItemRow < 21
        lastItemRow = lastItemRow + 1
        If lastItemRow > 21 Then Exit Do
    Loop

    ' Check if we can add more rows (limit to row 21)
    If lastItemRow >= 21 Then
        MsgBox "Maximum 4 items allowed in this invoice format!", vbExclamation
        Exit Sub
    End If

    newRowNum = lastItemRow + 1

    With ws
        ' Copy formatting from the previous row
        .Rows(lastItemRow).Copy
        .Rows(newRowNum).PasteSpecial xlPasteFormats
        Application.CutCopyMode = False

        ' Clear the values but keep formatting
        .Range("A" & newRowNum & ":K" & newRowNum).ClearContents

        ' Set the Sr.No. for the new row
        .Cells(newRowNum, 1).Value = newRowNum - 17  ' Sr.No. starts from 1

        ' Setup formulas for the new row
        .Range("G" & newRowNum).Formula = "=IF(AND(D" & newRowNum & "<>"""",F" & newRowNum & "<>""""),D" & newRowNum & "*F" & newRowNum & ","""")"
        .Range("H" & newRowNum).Formula = "=IF(G" & newRowNum & "<>"""",G" & newRowNum & ","""")"
        .Range("I" & newRowNum).Value = "12"
        .Range("J" & newRowNum).Formula = "=IF(AND(H" & newRowNum & "<>"""",I" & newRowNum & "<>""""),H" & newRowNum & "*I" & newRowNum & "/100,"""")"
        .Range("K" & newRowNum).Formula = "=IF(AND(H" & newRowNum & "<>"""",J" & newRowNum & "<>""""),H" & newRowNum & "+J" & newRowNum & ","""")"

        ' Format the new row
        .Range("G" & newRowNum & ":K" & newRowNum).NumberFormat = "0.00"
        .Range("I" & newRowNum).NumberFormat = "0.00"
        .Rows(newRowNum).RowHeight = 32

        ' Update the summary calculations to include all rows
        Call UpdateMultiItemTaxCalculations(ws)
    End With

    MsgBox "New item row added successfully!", vbInformation
    Exit Sub

ErrorHandler:
    MsgBox "Error adding new item row: " & Err.Description, vbCritical
End Sub

Private Sub RemoveLastItemRow()
    Dim ws As Worksheet
    Dim lastItemRow As Long
    Dim response As VbMsgBoxResult

    On Error GoTo ErrorHandler

    Set ws = GetOrCreateWorksheet("GST_Tax_Invoice_for_interstate")

    If ws Is Nothing Then
        MsgBox "Invoice sheet not found!", vbExclamation
        Exit Sub
    End If

    ' Find the last item row with data
    lastItemRow = 21
    Do While ws.Cells(lastItemRow, 1).Value = "" And lastItemRow > 18
        lastItemRow = lastItemRow - 1
    Loop

    ' Check if we can remove (must keep at least one row)
    If lastItemRow <= 18 Then
        MsgBox "Cannot remove the first item row!", vbExclamation
        Exit Sub
    End If

    response = MsgBox("Are you sure you want to remove the last item row?", vbYesNo + vbQuestion, "Remove Item Row")

    If response = vbYes Then
        With ws
            ' Clear the last row
            .Range("A" & lastItemRow & ":K" & lastItemRow).ClearContents

            ' Reset formatting to empty row style
            .Range("A" & lastItemRow & ":K" & lastItemRow).Interior.Color = IIf(lastItemRow Mod 2 = 0, RGB(250, 250, 250), RGB(255, 255, 255))

            ' Update the summary calculations
            Call UpdateMultiItemTaxCalculations(ws)
        End With

        MsgBox "Item row removed successfully!", vbInformation
    End If

    Exit Sub

ErrorHandler:
    MsgBox "Error removing item row: " & Err.Description, vbCritical
End Sub

Private Sub UpdateMultiItemTaxCalculations(ws As Worksheet)
    ' Update tax calculations to sum all item rows dynamically
    On Error Resume Next

    ' Find the last row with item data
    Dim lastItemRow As Long
    lastItemRow = 21 ' Start from default last row
    Do While lastItemRow < 30 And (ws.Cells(lastItemRow + 1, "A").Value <> "" Or ws.Cells(lastItemRow + 1, "B").Value <> "")
        lastItemRow = lastItemRow + 1
    Loop

    With ws
        ' Row 22: Total Quantity (sum of all quantities from D18 to last item row)
        .Range("D22").Formula = "=SUM(D18:D" & lastItemRow & ")"
        .Range("D22").NumberFormat = "#,##0.00"

        ' Row 22: Sub Total calculations
        .Range("G22").Formula = "=SUM(G18:G" & lastItemRow & ")"  ' Amount column
        .Range("H22").Formula = "=SUM(H18:H" & lastItemRow & ")"  ' Taxable Value column
        .Range("G22:H22").NumberFormat = "#,##0.00"

        ' Row 23: Total Amount Before Tax (sum of all taxable values from H18 to last item row)
        .Range("K23").Formula = "=SUM(H18:H" & lastItemRow & ")"

        ' Row 24: CGST (0 for interstate)
        .Range("K24").Value = 0

        ' Row 25: SGST (0 for interstate)
        .Range("K25").Value = 0

        ' Row 26: IGST (sum of all IGST amounts from I18 to last item row)
        .Range("K26").Formula = "=SUM(I18:I" & lastItemRow & ")"

        ' Row 27: CESS (0 by default)
        .Range("K27").Value = 0

        ' Row 28: Total Tax
        .Range("K28").Formula = "=K24+K25+K26+K27"

        ' Row 29-30: Total Amount After Tax
        .Range("K29").Formula = "=K23+K28"
        .Range("K30").Formula = "=K29"

        ' Format all calculation cells
        .Range("K23:K30").NumberFormat = "#,##0.00"

        ' Update Amount in Words (A24:G25 merged cell)
        Dim totalAmount As Double
        totalAmount = .Range("K29").Value
        If totalAmount > 0 Then
            .Range("A24").Value = NumberToWords(totalAmount)
        Else
            .Range("A24").Value = ""
        End If
    End With

    On Error GoTo 0
End Sub

Private Sub ClearAllItems()
    Dim ws As Worksheet
    Dim response As VbMsgBoxResult
    Dim i As Integer

    On Error GoTo ErrorHandler

    response = MsgBox("Are you sure you want to clear all item data? This cannot be undone.", vbYesNo + vbQuestion, "Clear All Items")

    If response = vbNo Then Exit Sub

    Set ws = GetOrCreateWorksheet("GST_Tax_Invoice_for_interstate")

    If ws Is Nothing Then
        MsgBox "Invoice sheet not found!", vbExclamation
        Exit Sub
    End If

    With ws
        ' Clear all item rows (18-21)
        For i = 18 To 21
            .Range("A" & i & ":K" & i).ClearContents
            ' Reset formatting to empty row style
            .Range("A" & i & ":K" & i).Interior.Color = IIf(i Mod 2 = 0, RGB(250, 250, 250), RGB(255, 255, 255))
        Next i

        ' Reset the first row with Sr.No. 1 and formulas
        .Cells(18, 1).Value = "1"
        Call SetupTaxCalculationFormulas(ws)
        Call UpdateMultiItemTaxCalculations(ws)
    End With

    MsgBox "All items cleared successfully!", vbInformation
    Exit Sub

ErrorHandler:
    MsgBox "Error clearing items: " & Err.Description, vbCritical
End Sub



' ▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓
' 📝 DATA VALIDATION & DROPDOWN FUNCTIONS
' ▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓

Private Sub SetupDataValidation(ws As Worksheet)
    ' Setup data validation dropdowns for standardized inputs
    On Error Resume Next

    ' Ensure supporting worksheets exist
    Call EnsureAllSupportingWorksheetsExist

    Dim validationWs As Worksheet
    Set validationWs = GetOrCreateWorksheet("warehouse")

    If validationWs Is Nothing Then
        Exit Sub
    End If

    With ws
        ' UOM dropdown with manual text entry capability (Column E: 18-21)
        ' Allow both dropdown selection AND manual text entry
        .Range("E18:E21").Validation.Delete
        .Range("E18:E21").Validation.Add Type:=xlValidateList, _
            AlertStyle:=xlValidAlertInformation, _
            Formula1:="=warehouse!$G$2:$G$11"  ' UOM list from column G
        .Range("E18:E21").Validation.IgnoreBlank = True
        .Range("E18:E21").Validation.InCellDropdown = True
        .Range("E18:E21").Validation.ShowError = False  ' Allow manual text entry
        .Range("E18:E21").Font.Color = RGB(26, 26, 26)  ' Standard black font

        ' Transport Mode dropdown with manual text entry capability (F7)
        ' Allow both dropdown selection AND manual text entry
        .Range("F7").Validation.Delete
        .Range("F7").Validation.Add Type:=xlValidateList, _
            AlertStyle:=xlValidAlertInformation, _
            Formula1:="=warehouse!$H$2:$H$8"  ' Transport modes from column H
        .Range("F7").Validation.IgnoreBlank = True
        .Range("F7").Validation.InCellDropdown = True
        .Range("F7").Validation.ShowError = False  ' Allow manual text entry

        ' Set default transport mode
        If .Range("F7").Value = "" Then
            .Range("F7").Value = "By Lorry"
        End If

        ' State dropdown for Receiver (Row 15, Column C15:F15)
        .Range("C15").Validation.Delete
        .Range("C15").Validation.Add Type:=xlValidateList, _
            AlertStyle:=xlValidAlertInformation, _
            Formula1:="=warehouse!$J$2:$J$11"  ' State list from column J
        .Range("C15").Validation.IgnoreBlank = True
        .Range("C15").Validation.InCellDropdown = True
        .Range("C15").Validation.ShowError = False  ' Allow manual text entry
        .Range("C15").Font.Color = RGB(26, 26, 26)  ' Standard black font

        ' State dropdown for Consignee (Row 15, Column I15:K15)
        .Range("I15").Validation.Delete
        .Range("I15").Validation.Add Type:=xlValidateList, _
            AlertStyle:=xlValidAlertInformation, _
            Formula1:="=warehouse!$J$2:$J$11"  ' State list from column J
        .Range("I15").Validation.IgnoreBlank = True
        .Range("I15").Validation.InCellDropdown = True
        .Range("I15").Validation.ShowError = False  ' Allow manual text entry
        .Range("I15").Font.Color = RGB(26, 26, 26)  ' Standard black font

        ' State Code dropdown for Receiver (Row 16, Column C16) - shows simple numeric codes
        .Range("C16").Validation.Delete
        .Range("C16").Validation.Add Type:=xlValidateList, _
            AlertStyle:=xlValidAlertInformation, _
            Formula1:="=warehouse!$K$2:$K$11"  ' Simple state code list from column K
        .Range("C16").Validation.IgnoreBlank = True
        .Range("C16").Validation.InCellDropdown = True
        .Range("C16").Validation.ShowError = False  ' Allow manual text entry
        .Range("C16").Font.Color = RGB(26, 26, 26)  ' Standard black font

        ' State Code dropdown for Consignee (Row 16, Column I16) - shows simple numeric codes
        .Range("I16").Validation.Delete
        .Range("I16").Validation.Add Type:=xlValidateList, _
            AlertStyle:=xlValidAlertInformation, _
            Formula1:="=warehouse!$K$2:$K$11"  ' Simple state code list from column K
        .Range("I16").Validation.IgnoreBlank = True
        .Range("I16").Validation.InCellDropdown = True
        .Range("I16").Validation.ShowError = False  ' Allow manual text entry
        .Range("I16").Font.Color = RGB(26, 26, 26)  ' Standard black font

    End With

    On Error GoTo 0
End Sub



' ===== CUSTOMER DATABASE INTEGRATION =====

Private Sub SetupCustomerDropdown(ws As Worksheet)
    ' Setup customer dropdown and auto-population
    On Error Resume Next

    ' Ensure supporting worksheets exist
    Call EnsureAllSupportingWorksheetsExist

    Dim dropdownWs As Worksheet
    Set dropdownWs = GetOrCreateWorksheet("warehouse")

    If dropdownWs Is Nothing Then
        Exit Sub
    End If

    With ws
        ' Customer dropdown with manual text entry capability for Receiver (row 12, column C)
        ' Allow both dropdown selection AND manual text entry
        .Range("C12").Validation.Delete
        .Range("C12").Validation.Add Type:=xlValidateList, _
            AlertStyle:=xlValidAlertInformation, _
            Formula1:="=warehouse!$M$2:$M$10"  ' Customer names (restored to column M)
        .Range("C12").Validation.IgnoreBlank = True
        .Range("C12").Validation.InCellDropdown = True
        .Range("C12").Validation.ShowError = False  ' Allow manual text entry
        .Range("C12").Font.Bold = True
        .Range("C12").Interior.Color = RGB(255, 255, 255)  ' White background
        .Range("C12").Font.Color = RGB(26, 26, 26)  ' Standard black font

        ' Customer dropdown with manual text entry capability for Consignee (row 12, column I)
        ' Allow both dropdown selection AND manual text entry
        .Range("I12").Validation.Delete
        .Range("I12").Validation.Add Type:=xlValidateList, _
            AlertStyle:=xlValidAlertInformation, _
            Formula1:="=warehouse!$M$2:$M$10"  ' Customer names (restored to column M)
        .Range("I12").Validation.IgnoreBlank = True
        .Range("I12").Validation.InCellDropdown = True
        .Range("I12").Validation.ShowError = False  ' Allow manual text entry
        .Range("I12").Font.Bold = True
        .Range("I12").Interior.Color = RGB(255, 255, 255)  ' White background
        .Range("I12").Font.Color = RGB(26, 26, 26)  ' Standard black font

        ' Set fixed state code for Andhra Pradesh (no dropdown needed)
        .Range("C10").Validation.Delete  ' Remove any existing validation
        .Range("C10").Value = "37"  ' Fixed value for Andhra Pradesh
        .Range("C10").Font.Bold = True
        .Range("C10").Interior.Color = RGB(245, 245, 245)  ' Light grey background
        .Range("C10").Font.Color = RGB(26, 26, 26)  ' Dark text
        .Range("C10").HorizontalAlignment = xlLeft
    End With

    On Error GoTo 0
End Sub



Private Sub PopulateCustomerDetails(ws As Worksheet, customerName As String)
    ' Automatically populate customer details when customer is selected
    Dim customerDetails As Variant

    If customerName = "" Then Exit Sub

    customerDetails = GetCustomerDetails(customerName)

    With ws
        ' Populate customer details in the party details section
        .Range("C13").Value = customerDetails(2)  ' Address
        .Range("C14").Value = customerDetails(8)  ' GSTIN
        .Range("C15").Value = customerDetails(5)  ' State
        .Range("C16").Value = customerDetails(6)  ' State Code

        ' Keep state code fixed as "37" for Andhra Pradesh (don't override)
        .Range("C10").Value = "37"  ' Always keep as 37 for Andhra Pradesh
    End With
End Sub

Private Function GetCustomerDetails(customerName As String) As Variant
    ' Get customer details from warehouse sheet (Customer section - columns M-T)
    Dim dropdownWs As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim customerDetails(11) As String  ' Array to hold customer details

    On Error GoTo ErrorHandler

    ' Ensure supporting worksheets exist
    Call EnsureAllSupportingWorksheetsExist

    Set dropdownWs = GetOrCreateWorksheet("warehouse")

    If dropdownWs Is Nothing Then
        GetCustomerDetails = customerDetails
        Exit Function
    End If

    ' Customer data starts at row 2, column M (Customer_Name is in column M)
    lastRow = dropdownWs.Cells(dropdownWs.Rows.Count, 13).End(xlUp).Row

    For i = 2 To lastRow
        If UCase(dropdownWs.Cells(i, 13).Value) = UCase(customerName) Then
            ' Found the customer, populate details array (restored to original structure)
            customerDetails(0) = ""                             ' Customer_ID (not in structure)
            customerDetails(1) = dropdownWs.Cells(i, 13).Value  ' Customer_Name (Column M)
            customerDetails(2) = dropdownWs.Cells(i, 14).Value  ' Address_Line1 (Column N)
            customerDetails(3) = ""                             ' Address_Line2 (not in structure)
            customerDetails(4) = ""                             ' City (not in structure)
            customerDetails(5) = dropdownWs.Cells(i, 15).Value  ' State (Column O)
            customerDetails(6) = dropdownWs.Cells(i, 16).Value  ' State_Code (Column P)
            customerDetails(7) = ""                             ' PIN_Code (not in structure)
            customerDetails(8) = dropdownWs.Cells(i, 17).Value  ' GSTIN (Column Q)
            customerDetails(9) = dropdownWs.Cells(i, 18).Value  ' Phone (Column R)
            customerDetails(10) = dropdownWs.Cells(i, 19).Value ' Email (Column S)
            customerDetails(11) = dropdownWs.Cells(i, 20).Value ' Contact_Person (Column T)
            Exit For
        End If
    Next i

    GetCustomerDetails = customerDetails
    Exit Function

ErrorHandler:
    GetCustomerDetails = customerDetails
End Function



' ===== HSN/SAC CODE LOOKUP SYSTEM =====

Private Sub SetupHSNDropdown(ws As Worksheet)
    ' Setup HSN code dropdown for item rows
    On Error Resume Next

    ' Ensure supporting worksheets exist
    Call EnsureAllSupportingWorksheetsExist

    Dim dropdownWs As Worksheet
    Set dropdownWs = GetOrCreateWorksheet("warehouse")

    If dropdownWs Is Nothing Then
        Exit Sub
    End If

    With ws
        ' HSN Code dropdown with manual text entry capability (Column C: 18-21)
        ' Allow both dropdown selection AND manual text entry
        .Range("C18:C21").Validation.Delete
        .Range("C18:C21").Validation.Add Type:=xlValidateList, _
            AlertStyle:=xlValidAlertInformation, _
            Formula1:="=warehouse!$A$2:$A$20"  ' HSN codes from column A
        .Range("C18:C21").Validation.IgnoreBlank = True
        .Range("C18:C21").Validation.InCellDropdown = True
        .Range("C18:C21").Validation.ShowError = False  ' Allow manual text entry
        .Range("C18:C21").Font.Color = RGB(26, 26, 26)  ' Standard black font
    End With

    On Error GoTo 0
End Sub

Private Function GetHSNDetails(hsnCode As String) As Variant
    ' Get HSN details from warehouse sheet (HSN section - columns A-E)
    Dim dropdownWs As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim hsnDetails(7) As String  ' Array to hold HSN details

    On Error GoTo ErrorHandler

    ' Ensure supporting worksheets exist
    Call EnsureAllSupportingWorksheetsExist

    Set dropdownWs = GetOrCreateWorksheet("warehouse")

    If dropdownWs Is Nothing Then
        GetHSNDetails = hsnDetails
        Exit Function
    End If

    ' HSN data starts at row 2, column A (HSN_Code is in column A)
    lastRow = dropdownWs.Cells(dropdownWs.Rows.Count, 1).End(xlUp).Row

    For i = 2 To lastRow
        If UCase(dropdownWs.Cells(i, 1).Value) = UCase(hsnCode) Then
            ' Found the HSN code, get all details
            hsnDetails(0) = dropdownWs.Cells(i, 1).Value  ' HSN_Code (Column A)
            hsnDetails(1) = dropdownWs.Cells(i, 2).Value  ' Description (Column B)
            hsnDetails(2) = ""                            ' UOM (not in new structure)
            hsnDetails(3) = dropdownWs.Cells(i, 3).Value  ' CGST_Rate (Column C)
            hsnDetails(4) = dropdownWs.Cells(i, 4).Value  ' SGST_Rate (Column D)
            hsnDetails(5) = dropdownWs.Cells(i, 5).Value  ' IGST_Rate (Column E)
            hsnDetails(6) = ""                            ' CESS_Rate (not in new structure)
            hsnDetails(7) = ""                            ' Category (not in new structure)
            Exit For
        End If
    Next i

    GetHSNDetails = hsnDetails
    Exit Function

ErrorHandler:
    GetHSNDetails = hsnDetails
End Function

Private Sub PopulateHSNDetails(targetCell As Range)
    ' Auto-populate HSN details when HSN code is selected
    Dim ws As Worksheet
    Dim hsnCode As String
    Dim hsnDetails As Variant
    Dim rowNum As Long

    On Error GoTo ErrorHandler

    Set ws = targetCell.Worksheet
    If ws.Name <> "GST_Tax_Invoice_for_interstate" Then Exit Sub

    hsnCode = targetCell.Value
    If hsnCode = "" Then Exit Sub

    rowNum = targetCell.Row
    hsnDetails = GetHSNDetails(hsnCode)

    ' Populate description (Column B)
    If hsnDetails(1) <> "" Then
        ws.Cells(rowNum, 2).Value = hsnDetails(1)
    End If

    ' Populate UOM (Column E)
    If hsnDetails(2) <> "" Then
        ws.Cells(rowNum, 5).Value = hsnDetails(2)
    End If

    ' Populate IGST Rate (Column I) - for interstate sales
    If hsnDetails(5) <> "" Then
        ws.Cells(rowNum, 9).Value = hsnDetails(5)
    End If

    Exit Sub

ErrorHandler:
    MsgBox "Error populating HSN details: " & Err.Description, vbCritical
End Sub

Private Sub AddHSNToMaster()
    ' Add new HSN code to warehouse sheet (HSN section - columns A-E)
    Dim dropdownWs As Worksheet
    Dim lastRow As Long

    On Error GoTo ErrorHandler

    ' Ensure supporting worksheets exist
    Call EnsureAllSupportingWorksheetsExist

    Set dropdownWs = GetOrCreateWorksheet("warehouse")

    If dropdownWs Is Nothing Then
        MsgBox "warehouse sheet not found!", vbExclamation
        Exit Sub
    End If

    ' HSN data starts at row 2, find last row in HSN section (column A)
    lastRow = dropdownWs.Cells(dropdownWs.Rows.Count, 1).End(xlUp).Row

    ' Simple input form (you can enhance this with a UserForm)
    Dim hsnCode As String, description As String
    Dim cgstRate As String, sgstRate As String, igstRate As String

    hsnCode = InputBox("Enter HSN/SAC Code:", "Add New HSN Code")
    If hsnCode = "" Then Exit Sub

    description = InputBox("Enter Description:", "Add New HSN Code")
    cgstRate = InputBox("Enter CGST Rate (%):", "Add New HSN Code")
    sgstRate = InputBox("Enter SGST Rate (%):", "Add New HSN Code")
    igstRate = InputBox("Enter IGST Rate (%):", "Add New HSN Code")

    With dropdownWs
        ' Add HSN data to columns A-E (new structure)
        .Cells(lastRow + 1, 1).Value = hsnCode           ' Column A - HSN_Code
        .Cells(lastRow + 1, 2).Value = description       ' Column B - Description
        .Cells(lastRow + 1, 3).Value = Val(cgstRate)     ' Column C - CGST_Rate
        .Cells(lastRow + 1, 4).Value = Val(sgstRate)     ' Column D - SGST_Rate
        .Cells(lastRow + 1, 5).Value = Val(igstRate)     ' Column E - IGST_Rate

        ' Add borders
        .Range("A" & lastRow + 1 & ":E" & lastRow + 1).Borders.LineStyle = xlContinuous
        .Range("A" & lastRow + 1 & ":E" & lastRow + 1).Borders.Color = RGB(204, 204, 204)
    End With

    MsgBox "HSN code added successfully!", vbInformation
    Exit Sub

ErrorHandler:
    MsgBox "Error adding HSN code: " & Err.Description, vbCritical
End Sub

' ████████████████████████████████████████████████████████████████████████████████
' 🔘 BUTTON FUNCTIONS - DAILY OPERATIONS
' ████████████████████████████████████████████████████████████████████████████████
' These functions are designed to be assigned to Excel buttons for daily use.

Sub AddCustomerToWarehouseButton()
    ' Button function: Capture customer details from current invoice and save to warehouse
    On Error GoTo ErrorHandler

    Dim invoiceWs As Worksheet
    Dim warehouseWs As Worksheet

    ' Get worksheets
    Set invoiceWs = ThisWorkbook.Worksheets("GST_Tax_Invoice_for_interstate")
    Set warehouseWs = ThisWorkbook.Worksheets("warehouse")

    ' Get customer details from invoice
    Dim customerName As String, address As String, gstin As String, stateCode As String
    customerName = Trim(invoiceWs.Range("C12").Value)
    address = Trim(invoiceWs.Range("C13").Value & " " & invoiceWs.Range("C14").Value & " " & invoiceWs.Range("C15").Value)
    gstin = Trim(invoiceWs.Range("C16").Value)
    stateCode = Trim(invoiceWs.Range("C10").Value)

    ' Validate required fields
    If customerName = "" Then
        MsgBox "Please enter customer name before adding to warehouse.", vbExclamation, "Missing Information"
        Exit Sub
    End If

    ' Check for duplicates in warehouse (Customer section - columns M-T)
    Dim lastRow As Long
    lastRow = warehouseWs.Cells(warehouseWs.Rows.Count, "M").End(xlUp).Row

    Dim i As Long
    For i = 2 To lastRow ' Start from row 2 (skip header)
        If UCase(Trim(warehouseWs.Cells(i, "M").Value)) = UCase(customerName) Then
            MsgBox "Customer '" & customerName & "' already exists in warehouse.", vbInformation, "Duplicate Customer"
            Exit Sub
        End If
    Next i

    ' Add new customer to next available row
    Dim newRow As Long
    newRow = lastRow + 1

    warehouseWs.Cells(newRow, "M").Value = customerName     ' Column M: Customer Name
    warehouseWs.Cells(newRow, "N").Value = address          ' Column N: Address
    warehouseWs.Cells(newRow, "O").Value = ""               ' Column O: State (empty for now)
    warehouseWs.Cells(newRow, "P").Value = stateCode        ' Column P: State Code
    warehouseWs.Cells(newRow, "Q").Value = gstin            ' Column Q: GSTIN
    warehouseWs.Cells(newRow, "R").Value = ""               ' Column R: Phone (empty)
    warehouseWs.Cells(newRow, "S").Value = ""               ' Column S: Email (empty)
    warehouseWs.Cells(newRow, "T").Value = ""               ' Column T: Contact Person (empty)

    MsgBox "Customer '" & customerName & "' added successfully to warehouse!", vbInformation, "Customer Added"
    Exit Sub

ErrorHandler:
    MsgBox "Error adding customer: " & Err.Description, vbCritical, "Error"
End Sub

Sub AddNewItemRowButton()
    ' Button function: Add new item row after existing item rows with clean layout
    On Error GoTo ErrorHandler

    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("GST_Tax_Invoice_for_interstate")

    ' Find the last used item row (starting from row 18)
    Dim lastItemRow As Long
    lastItemRow = 21 ' Start checking from the last default row

    ' Find the actual last row with item data
    Do While lastItemRow < 30 And (ws.Cells(lastItemRow + 1, "A").Value <> "" Or ws.Cells(lastItemRow + 1, "B").Value <> "")
        lastItemRow = lastItemRow + 1
    Loop

    ' Insert new row immediately after the last item row
    Dim insertRow As Long
    insertRow = lastItemRow + 1

    ' Safety check - don't allow more than 10 total item rows to preserve layout
    If insertRow > 28 Then
        MsgBox "Cannot add more rows. Maximum limit reached to preserve invoice structure.", vbExclamation, "Row Limit"
        Exit Sub
    End If

    ' Insert new row
    ws.Rows(insertRow).Insert Shift:=xlDown

    ' Copy formatting from row 21 (last default item row) to maintain consistency
    ws.Rows(21).Copy
    ws.Rows(insertRow).PasteSpecial Paste:=xlPasteFormats
    Application.CutCopyMode = False

    ' Set up the new row with proper structure
    With ws
        ' Clear any existing content
        .Range("A" & insertRow & ":O" & insertRow).ClearContents

        ' Ensure proper borders for the item table area (A to O columns only)
        .Range("A" & insertRow & ":O" & insertRow).Borders.LineStyle = xlContinuous
        .Range("A" & insertRow & ":O" & insertRow).Borders.Color = RGB(204, 204, 204)
        .Range("A" & insertRow & ":O" & insertRow).Borders.Weight = xlThin

        ' Set row height to match other item rows
        .Rows(insertRow).RowHeight = 25

        ' Add proper formulas for calculations (columns H, I, J, K)
        .Cells(insertRow, "H").Formula = "=IF(AND(F" & insertRow & ">0,G" & insertRow & ">0),F" & insertRow & "*G" & insertRow & ","""")" ' Taxable Value
        .Cells(insertRow, "I").Formula = "=IF(H" & insertRow & ">0,H" & insertRow & "*E" & insertRow & "/100,"""")" ' IGST Amount
        .Cells(insertRow, "J").Formula = "=IF(H" & insertRow & ">0,H" & insertRow & "+I" & insertRow & ","""")" ' Total Amount
        .Cells(insertRow, "K").Formula = "=""""" ' CESS (empty for now)

        ' Set proper alignment and formatting
        .Range("A" & insertRow & ":D" & insertRow).HorizontalAlignment = xlLeft
        .Range("E" & insertRow & ":K" & insertRow).HorizontalAlignment = xlCenter

        ' Apply red color formatting for user input cells
        .Range("A" & insertRow & ":G" & insertRow).Font.Color = RGB(220, 20, 60)
        .Range("A" & insertRow & ":G" & insertRow).Font.Bold = True

        ' Clear any content outside the invoice area to prevent #VALUE errors
        .Range("L" & insertRow & ":Z" & insertRow).Clear
        .Range("L" & insertRow & ":Z" & insertRow).ClearFormats
    End With

    ' Update tax calculation formulas to include new row
    Call UpdateMultiItemTaxCalculations(ws)

    MsgBox "New item row added successfully at row " & insertRow & "!" & vbCrLf & "Ready for item entry.", vbInformation, "Row Added"

    ' Select the first cell of the new row for data entry
    ws.Range("A" & insertRow).Select
    Exit Sub

ErrorHandler:
    MsgBox "Error adding new row: " & Err.Description, vbCritical, "Error"
End Sub

' RemoveRowButton function has been completely removed for cleaner interface
' Users can manually clear row content if needed

Sub NewInvoiceButton()
    ' Button function: Generate a fresh invoice with next sequential number and cleared fields
    On Error GoTo ErrorHandler

    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("GST_Tax_Invoice_for_interstate")

    ' Confirm creating new invoice
    Dim response As VbMsgBoxResult
    response = MsgBox("Create a new invoice?" & vbCrLf & "All current data will be cleared and a new invoice number will be generated.", vbYesNo + vbQuestion, "Confirm New Invoice")
    If response = vbNo Then Exit Sub

    ' Generate next sequential invoice number
    Dim nextInvoiceNumber As String
    nextInvoiceNumber = GetNextInvoiceNumber()

    ' Clear and set invoice number (C7) with new sequential number
    With ws.Range("C7")
        .Value = nextInvoiceNumber
        .Font.Bold = True
        .Font.Color = RGB(220, 20, 60)  ' Red color for user input
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
    End With

    ' Set current date for Invoice Date (C8) and Date of Supply (F9, G9)
    With ws.Range("C8")
        .Value = Format(Date, "dd/mm/yyyy")
        .Font.Bold = True
        .HorizontalAlignment = xlLeft
        .VerticalAlignment = xlCenter
    End With

    With ws.Range("F9")
        .Value = Format(Date, "dd/mm/yyyy")
        .Font.Bold = True
        .HorizontalAlignment = xlLeft
        .VerticalAlignment = xlCenter
    End With

    With ws.Range("G9")
        .Value = Format(Date, "dd/mm/yyyy")
        .Font.Bold = True
        .HorizontalAlignment = xlLeft
        .VerticalAlignment = xlCenter
    End With

    ' Reset state code to default (C10)
    With ws.Range("C10")
        .Value = "37"  ' Fixed value for Andhra Pradesh
        .Font.Bold = True
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
    End With

    ' Clear all customer details (handle merged cells properly)
    On Error Resume Next
    ' Clear individual cells to avoid merged cell issues
    ws.Range("C12").ClearContents  ' Customer Name (Receiver)
    ws.Range("C13").ClearContents  ' Address (Receiver)
    ws.Range("C14").ClearContents  ' GSTIN (Receiver)
    ws.Range("C15").ClearContents  ' State (Receiver)
    ws.Range("C16").ClearContents  ' State Code (Receiver)

    ws.Range("I12").ClearContents  ' Customer Name (Consignee)
    ws.Range("I13").ClearContents  ' Address (Consignee)
    ws.Range("I14").ClearContents  ' GSTIN (Consignee)
    ws.Range("I15").ClearContents  ' State (Consignee)
    ws.Range("I16").ClearContents  ' State Code (Consignee)
    On Error GoTo ErrorHandler

    ' Clear item table data (rows 18-25, keep headers) - avoid merged cells
    On Error Resume Next
    Dim clearRow As Long
    For clearRow = 18 To 25
        ws.Range("A" & clearRow & ":O" & clearRow).ClearContents
    Next clearRow
    On Error GoTo ErrorHandler

    ' Reset to minimum 4 default item rows (18-21) with proper formatting
    Dim i As Long
    For i = 18 To 21 ' Ensure minimum 4 item rows as per user requirement
        With ws
            ' Add borders
            .Range("A" & i & ":O" & i).Borders.LineStyle = xlContinuous
            .Range("A" & i & ":O" & i).Borders.Color = RGB(204, 204, 204)

            ' Set row height
            .Rows(i).RowHeight = 25

            ' Add formulas for calculations (columns L, M, N, O)
            .Cells(i, "L").Formula = "=IF(AND(J" & i & ">0,K" & i & ">0),J" & i & "*K" & i & ","""")" ' Taxable Value
            .Cells(i, "M").Formula = "=IF(L" & i & ">0,L" & i & "*0.18,"""")" ' IGST Amount (18%)
            .Cells(i, "N").Formula = "=IF(L" & i & ">0,L" & i & "+M" & i & ","""")" ' Total Amount
            .Cells(i, "O").Formula = "=""""" ' CESS (empty for now)

            ' Center align numeric columns
            .Range("J" & i & ":O" & i).HorizontalAlignment = xlCenter
        End With
    Next i

    ' Clear tax summary section (handle merged cells properly)
    On Error Resume Next
    ' Clear individual tax summary cells to avoid merged cell issues
    ws.Range("K23").ClearContents  ' Total Before Tax
    ws.Range("K24").ClearContents  ' CGST
    ws.Range("K25").ClearContents  ' SGST
    ws.Range("K26").ClearContents  ' IGST
    ws.Range("K27").ClearContents  ' CESS
    ws.Range("K28").ClearContents  ' Total Tax
    ws.Range("K29").ClearContents  ' Total After Tax
    ws.Range("K30").ClearContents  ' Grand Total
    On Error GoTo ErrorHandler

    ' Update tax calculations
    Call UpdateMultiItemTaxCalculations(ws)

    MsgBox "New invoice created successfully!" & vbCrLf & "Invoice Number: " & nextInvoiceNumber & vbCrLf & "Date: " & Format(Date, "dd/mm/yyyy"), vbInformation, "New Invoice Ready"

    ' Select customer name field for new entry
    ws.Range("C12").Select
    Exit Sub

ErrorHandler:
    MsgBox "Error creating new invoice: " & Err.Description, vbCritical, "Error"
End Sub

Sub SaveInvoiceButton()
    ' Button function: Save complete invoice record to Master sheet for future reference
    On Error GoTo ErrorHandler

    Dim invoiceWs As Worksheet
    Dim masterWs As Worksheet

    Set invoiceWs = ThisWorkbook.Worksheets("GST_Tax_Invoice_for_interstate")
    Set masterWs = ThisWorkbook.Worksheets("Master")

    ' Get invoice details for GST compliance
    Dim invoiceNumber As String, invoiceDate As String, customerName As String
    Dim customerGSTIN As String, customerState As String, customerStateCode As String
    Dim hsnCodes As String, itemDescriptions As String, totalQuantity As String, uomList As String

    invoiceNumber = Trim(invoiceWs.Range("C7").Value)
    invoiceDate = Trim(invoiceWs.Range("C8").Value)
    customerName = Trim(invoiceWs.Range("C12").Value)
    customerGSTIN = Trim(invoiceWs.Range("C14").Value)
    customerState = Trim(invoiceWs.Range("C15").Value)
    customerStateCode = Trim(invoiceWs.Range("C16").Value)

    ' Calculate totals and collect item details from item table
    Dim taxableTotal As Double, igstTotal As Double, grandTotal As Double, totalQty As Double
    Dim i As Long
    Dim igstRate As String

    For i = 18 To 25 ' Check all possible item rows
        If invoiceWs.Cells(i, "L").Value <> "" And IsNumeric(invoiceWs.Cells(i, "L").Value) Then
            taxableTotal = taxableTotal + invoiceWs.Cells(i, "L").Value
        End If
        If invoiceWs.Cells(i, "M").Value <> "" And IsNumeric(invoiceWs.Cells(i, "M").Value) Then
            igstTotal = igstTotal + invoiceWs.Cells(i, "M").Value
        End If
        If invoiceWs.Cells(i, "N").Value <> "" And IsNumeric(invoiceWs.Cells(i, "N").Value) Then
            grandTotal = grandTotal + invoiceWs.Cells(i, "N").Value
        End If

        ' Collect item details for GST audit
        If Trim(invoiceWs.Cells(i, "B").Value) <> "" Then ' HSN Code
            If hsnCodes <> "" Then hsnCodes = hsnCodes & "; "
            hsnCodes = hsnCodes & Trim(invoiceWs.Cells(i, "B").Value)
        End If
        If Trim(invoiceWs.Cells(i, "C").Value) <> "" Then ' Item Description
            If itemDescriptions <> "" Then itemDescriptions = itemDescriptions & "; "
            itemDescriptions = itemDescriptions & Trim(invoiceWs.Cells(i, "C").Value)
        End If
        If invoiceWs.Cells(i, "J").Value <> "" And IsNumeric(invoiceWs.Cells(i, "J").Value) Then ' Quantity
            totalQty = totalQty + invoiceWs.Cells(i, "J").Value
        End If
        If Trim(invoiceWs.Cells(i, "I").Value) <> "" Then ' UOM
            If uomList <> "" And InStr(uomList, Trim(invoiceWs.Cells(i, "I").Value)) = 0 Then
                uomList = uomList & "; "
            End If
            If InStr(uomList, Trim(invoiceWs.Cells(i, "I").Value)) = 0 Then
                uomList = uomList & Trim(invoiceWs.Cells(i, "I").Value)
            End If
        End If
    Next i

    ' Calculate IGST rate (assuming 18% for interstate)
    If taxableTotal > 0 Then
        igstRate = Format((igstTotal / taxableTotal) * 100, "0.00") & "%"
    Else
        igstRate = "18.00%"
    End If

    ' Validate required fields
    If invoiceNumber = "" Or customerName = "" Then
        MsgBox "Please ensure invoice number and customer name are filled before saving.", vbExclamation, "Missing Information"
        Exit Sub
    End If

    ' Check if invoice already exists in Master sheet (starting from row 2, after headers)
    Dim lastRow As Long
    lastRow = masterWs.Cells(masterWs.Rows.Count, "A").End(xlUp).Row
    If lastRow < 2 Then lastRow = 1 ' Ensure we start after the header row

    For i = 2 To lastRow ' Start from row 2 (skip header row)
        If Trim(masterWs.Cells(i, "A").Value) = invoiceNumber Then
            Dim response As VbMsgBoxResult
            response = MsgBox("Invoice " & invoiceNumber & " already exists in Master sheet." & vbCrLf & "Update existing record?", vbYesNo + vbQuestion, "Duplicate Invoice")
            If response = vbNo Then Exit Sub
            ' Update existing record
            GoTo UpdateRecord
        End If
    Next i

    ' Add new record
    lastRow = lastRow + 1

UpdateRecord:
    ' Save complete GST-compliant invoice data to Master sheet
    With masterWs
        .Cells(lastRow, "A").Value = invoiceNumber          ' Column A: Invoice_Number
        .Cells(lastRow, "B").Value = invoiceDate            ' Column B: Invoice_Date
        .Cells(lastRow, "C").Value = customerName           ' Column C: Customer_Name
        .Cells(lastRow, "D").Value = customerGSTIN          ' Column D: Customer_GSTIN
        .Cells(lastRow, "E").Value = customerState          ' Column E: Customer_State
        .Cells(lastRow, "F").Value = customerStateCode      ' Column F: Customer_State_Code
        .Cells(lastRow, "G").Value = taxableTotal           ' Column G: Total_Taxable_Value
        .Cells(lastRow, "H").Value = igstRate               ' Column H: IGST_Rate
        .Cells(lastRow, "I").Value = igstTotal              ' Column I: IGST_Amount
        .Cells(lastRow, "J").Value = igstTotal              ' Column J: Total_Tax_Amount (same as IGST for interstate)
        .Cells(lastRow, "K").Value = grandTotal             ' Column K: Total_Invoice_Value
        .Cells(lastRow, "L").Value = hsnCodes               ' Column L: HSN_Codes
        .Cells(lastRow, "M").Value = itemDescriptions       ' Column M: Item_Description
        .Cells(lastRow, "N").Value = totalQty               ' Column N: Quantity
        .Cells(lastRow, "O").Value = uomList                ' Column O: UOM
        .Cells(lastRow, "P").Value = Now                    ' Column P: Date_Created

        ' Add borders for the new record
        .Range("A" & lastRow & ":P" & lastRow).Borders.LineStyle = xlContinuous
        .Range("A" & lastRow & ":P" & lastRow).Borders.Color = RGB(204, 204, 204)
    End With

    MsgBox "Invoice " & invoiceNumber & " saved successfully to Master sheet!" & vbCrLf & _
           "Customer: " & customerName & vbCrLf & _
           "Total Taxable Value: ₹" & Format(taxableTotal, "#,##0.00") & vbCrLf & _
           "IGST Amount: ₹" & Format(igstTotal, "#,##0.00") & vbCrLf & _
           "Total Invoice Value: ₹" & Format(grandTotal, "#,##0.00") & vbCrLf & vbCrLf & _
           "Record saved for GST audit and return filing purposes.", vbInformation, "GST Invoice Record Saved"
    Exit Sub

ErrorHandler:
    MsgBox "Error saving invoice: " & Err.Description, vbCritical, "Error"
End Sub

Sub PrintAsPDFButton()
    ' Button function: Export invoice as PDF to designated folder
    On Error GoTo ErrorHandler

    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("GST_Tax_Invoice_for_interstate")

    ' Get invoice number for filename
    Dim invoiceNumber As String
    invoiceNumber = Trim(ws.Range("C7").Value)

    If invoiceNumber = "" Then
        MsgBox "Please ensure invoice number is filled before exporting to PDF.", vbExclamation, "Missing Invoice Number"
        Exit Sub
    End If

    ' Clean invoice number for filename (remove special characters)
    Dim cleanInvoiceNumber As String
    cleanInvoiceNumber = Replace(Replace(Replace(invoiceNumber, "/", "-"), "\", "-"), ":", "-")

    ' Set PDF export path
    Dim pdfPath As String
    pdfPath = "/Users/<USER>/BNC/gst invoices/"

    ' Create directory if it doesn't exist
    Dim fso As Object
    Set fso = CreateObject("Scripting.FileSystemObject")
    If Not fso.FolderExists(pdfPath) Then
        fso.CreateFolder pdfPath
    End If

    ' Full filename with path
    Dim fullPath As String
    fullPath = pdfPath & "Invoice_" & cleanInvoiceNumber & ".pdf"

    ' Set print area to cover the invoice (adjust range as needed)
    ws.PageSetup.PrintArea = "A1:O40"

    ' Configure page setup for better PDF output
    With ws.PageSetup
        .Orientation = xlPortrait
        .PaperSize = xlPaperA4
        .FitToPagesWide = 1
        .FitToPagesTall = 1
        .LeftMargin = Application.InchesToPoints(0.5)
        .RightMargin = Application.InchesToPoints(0.5)
        .TopMargin = Application.InchesToPoints(0.5)
        .BottomMargin = Application.InchesToPoints(0.5)
        .HeaderMargin = Application.InchesToPoints(0.3)
        .FooterMargin = Application.InchesToPoints(0.3)
        .CenterHorizontally = True
        .CenterVertically = False
    End With

    ' Export to PDF
    ws.ExportAsFixedFormat Type:=xlTypePDF, _
                          Filename:=fullPath, _
                          Quality:=xlQualityStandard, _
                          IncludeDocProps:=True, _
                          IgnorePrintAreas:=False, _
                          OpenAfterPublish:=False

    MsgBox "Invoice exported successfully!" & vbCrLf & _
           "File: Invoice_" & cleanInvoiceNumber & ".pdf" & vbCrLf & _
           "Location: " & pdfPath, vbInformation, "PDF Export Complete"
    Exit Sub

ErrorHandler:
    MsgBox "Error exporting PDF: " & Err.Description & vbCrLf & _
           "Please check if the folder path exists and you have write permissions.", vbCritical, "PDF Export Error"
End Sub

Sub PrintButton()
    ' Button function: Save as PDF and then send to default printer
    On Error GoTo ErrorHandler

    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("GST_Tax_Invoice_for_interstate")

    ' Get invoice number
    Dim invoiceNumber As String
    invoiceNumber = Trim(ws.Range("C7").Value)

    If invoiceNumber = "" Then
        MsgBox "Please ensure invoice number is filled before printing.", vbExclamation, "Missing Invoice Number"
        Exit Sub
    End If

    ' First, save as PDF (call the PDF export function)
    Call PrintAsPDFButton

    ' Configure print settings
    With ws.PageSetup
        .PrintArea = "A1:O40"
        .Orientation = xlPortrait
        .PaperSize = xlPaperA4
        .FitToPagesWide = 1
        .FitToPagesTall = 1
        .LeftMargin = Application.InchesToPoints(0.5)
        .RightMargin = Application.InchesToPoints(0.5)
        .TopMargin = Application.InchesToPoints(0.5)
        .BottomMargin = Application.InchesToPoints(0.5)
        .CenterHorizontally = True
        .CenterVertically = False
        .PrintComments = xlPrintNoComments
        .PrintErrors = xlPrintErrorsDisplayed
    End With

    ' Confirm printing
    Dim response As VbMsgBoxResult
    response = MsgBox("Send invoice " & invoiceNumber & " to printer?" & vbCrLf & _
                     "PDF has been saved to: /Users/<USER>/BNC/gst invoices/", _
                     vbYesNo + vbQuestion, "Confirm Print")

    If response = vbYes Then
        ' Print the invoice
        ws.PrintOut Copies:=1, Preview:=False, ActivePrinter:=""

        MsgBox "Invoice " & invoiceNumber & " sent to printer successfully!" & vbCrLf & _
               "PDF copy saved to: /Users/<USER>/BNC/gst invoices/", _
               vbInformation, "Print Complete"
    End If

    Exit Sub

ErrorHandler:
    MsgBox "Error printing invoice: " & Err.Description, vbCritical, "Print Error"
End Sub









' ===== AMOUNT IN WORDS CONVERSION SYSTEM =====
' Note: Amount in words functionality integrated into NumberToWords system
' Signature section uses rows 31-37 as per GST invoice format













' ████████████████████████████████████████████████████████████████████████████████
' ✅ END OF GST TAX INVOICE SYSTEM
' ████████████████████████████████████████████████████████████████████████████████
' Total Functions: 12 PUBLIC (visible) + 20+ PRIVATE (hidden)
' Professional VBA architecture with clean macro list interface
' ████████████████████████████████████████████████████████████████████████████████
